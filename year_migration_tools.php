<?php
// Include config file
require_once "config.php";

// Check if user is logged in and is admin
require_admin();

$success_message = "";
$error_message = "";
$preview_data = [];

// Get current academic year
$current_year = get_current_academic_year();

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST["preview_migration"])) {
        $from_year = trim($_POST["from_year"]);
        $to_year = trim($_POST["to_year"]);
        
        if (!empty($from_year) && !empty($to_year)) {
            // Preview migration
            $sql = "SELECT id, registration_number, name, education_level, created_at 
                    FROM applicants 
                    WHERE registration_number LIKE 'P{$from_year}.%' OR registration_number LIKE 'A{$from_year}.%' 
                    ORDER BY registration_number LIMIT 50";
            $result = mysqli_query($conn, $sql);
            
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $old_number = $row['registration_number'];
                    $new_number = str_replace(
                        [$from_year . '.'], 
                        [$to_year . '.'], 
                        $old_number
                    );
                    
                    $preview_data[] = [
                        'id' => $row['id'],
                        'name' => $row['name'],
                        'education_level' => $row['education_level'],
                        'old_number' => $old_number,
                        'new_number' => $new_number,
                        'created_at' => $row['created_at']
                    ];
                }
            }
        }
    } elseif (isset($_POST["execute_migration"])) {
        $from_year = trim($_POST["from_year"]);
        $to_year = trim($_POST["to_year"]);
        $migration_notes = trim($_POST["migration_notes"]);
        
        if (!empty($from_year) && !empty($to_year)) {
            mysqli_begin_transaction($conn);
            
            try {
                $update_count = 0;
                
                // Get all applicants with the old year format
                $sql = "SELECT id, registration_number FROM applicants 
                        WHERE registration_number LIKE 'P{$from_year}.%' OR registration_number LIKE 'A{$from_year}.%'";
                $result = mysqli_query($conn, $sql);
                
                if ($result) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        $id = $row['id'];
                        $old_number = $row['registration_number'];
                        $new_number = str_replace(
                            [$from_year . '.'], 
                            [$to_year . '.'], 
                            $old_number
                        );
                        
                        // Update registration number
                        $update_sql = "UPDATE applicants SET registration_number = ? WHERE id = ?";
                        $stmt = mysqli_prepare($conn, $update_sql);
                        mysqli_stmt_bind_param($stmt, "si", $new_number, $id);
                        
                        if (mysqli_stmt_execute($stmt)) {
                            $update_count++;
                            
                            // Log the change
                            $log_sql = "INSERT INTO registration_number_logs (applicant_id, old_number, new_number, changed_by) VALUES (?, ?, ?, ?)";
                            $log_stmt = mysqli_prepare($conn, $log_sql);
                            if ($log_stmt) {
                                $user_id = $_SESSION['user_id'];
                                mysqli_stmt_bind_param($log_stmt, "issi", $id, $old_number, $new_number, $user_id);
                                mysqli_stmt_execute($log_stmt);
                                mysqli_stmt_close($log_stmt);
                            }
                        }
                        
                        mysqli_stmt_close($stmt);
                    }
                }
                
                // Update academic year setting
                $year_update_sql = "UPDATE academic_year_settings SET setting_value = ?, updated_by = ? WHERE setting_name = 'current_academic_year'";
                $year_stmt = mysqli_prepare($conn, $year_update_sql);
                mysqli_stmt_bind_param($year_stmt, "si", $to_year, $_SESSION['user_id']);
                mysqli_stmt_execute($year_stmt);
                mysqli_stmt_close($year_stmt);
                
                // Log year transition
                $transition_sql = "INSERT INTO academic_year_history (old_year, new_year, transition_type, changed_by, notes, affected_registrations) VALUES (?, ?, 'manual', ?, ?, ?)";
                $transition_stmt = mysqli_prepare($conn, $transition_sql);
                mysqli_stmt_bind_param($transition_stmt, "ssisi", $from_year, $to_year, $_SESSION['user_id'], $migration_notes, $update_count);
                mysqli_stmt_execute($transition_stmt);
                mysqli_stmt_close($transition_stmt);
                
                mysqli_commit($conn);
                $success_message = "Migration berhasil! $update_count nomor pendaftaran telah diupdate dari $from_year ke $to_year.";
                
                // Refresh current year
                $current_year = get_current_academic_year();
                
            } catch (Exception $e) {
                mysqli_rollback($conn);
                $error_message = "Error during migration: " . $e->getMessage();
            }
        }
    }
}

// Get migration statistics
$stats = [];
$years = ['24', '25', '26', '27', '28', '29'];
foreach ($years as $year) {
    $count_sql = "SELECT COUNT(*) as count FROM applicants WHERE registration_number LIKE 'P{$year}.%' OR registration_number LIKE 'A{$year}.%'";
    $count_result = mysqli_query($conn, $count_sql);
    $stats[$year] = mysqli_fetch_assoc($count_result)['count'];
}

// Get recent migrations
$recent_migrations = [];
$recent_sql = "SELECT * FROM academic_year_history ORDER BY transition_date DESC LIMIT 5";
$recent_result = mysqli_query($conn, $recent_sql);
while ($row = mysqli_fetch_assoc($recent_result)) {
    $recent_migrations[] = $row;
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Year Migration Tools - Seminari Menengah St. Petrus Canisius</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-arrow-repeat me-2"></i>Year Migration Tools</h2>
                    <div>
                        <a href="academic_year_settings.php" class="btn btn-outline-primary me-2">
                            <i class="bi bi-gear me-1"></i>Settings
                        </a>
                        <a href="admin_dashboard.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-1"></i>Dashboard
                        </a>
                    </div>
                </div>

                <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle-fill me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i><?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-bar-chart-fill me-2"></i>Distribusi Nomor Pendaftaran per Tahun</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($stats as $year => $count): ?>
                                    <div class="col-md-2">
                                        <div class="card text-center <?php echo $year == $current_year ? 'border-primary' : ''; ?>">
                                            <div class="card-body py-2">
                                                <h4 class="<?php echo $year == $current_year ? 'text-primary' : ''; ?>"><?php echo $count; ?></h4>
                                                <small>Tahun <?php echo $year; ?> <?php echo $year == $current_year ? '(Current)' : ''; ?></small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Migration Form -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-arrow-right-circle me-2"></i>Bulk Year Migration</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <strong>Peringatan:</strong> Tool ini akan mengubah semua nomor pendaftaran dari tahun tertentu ke tahun baru. 
                                    Pastikan untuk backup database sebelum melakukan migration.
                                </div>

                                <form method="post">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Dari Tahun</label>
                                                <select name="from_year" class="form-select" required>
                                                    <option value="">Pilih tahun asal</option>
                                                    <?php foreach ($stats as $year => $count): ?>
                                                    <?php if ($count > 0): ?>
                                                    <option value="<?php echo $year; ?>"><?php echo $year; ?> (<?php echo $count; ?> records)</option>
                                                    <?php endif; ?>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Ke Tahun</label>
                                                <input type="text" name="to_year" class="form-control" 
                                                       placeholder="Contoh: 27" pattern="\d{2}" maxlength="2" required>
                                                <div class="form-text">Format 2 digit</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Catatan Migration</label>
                                        <textarea name="migration_notes" class="form-control" rows="2" 
                                                  placeholder="Catatan untuk migration ini (opsional)"></textarea>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" name="preview_migration" class="btn btn-outline-primary">
                                            <i class="bi bi-eye me-1"></i>Preview Migration
                                        </button>
                                        
                                        <?php if (!empty($preview_data)): ?>
                                        <button type="submit" name="execute_migration" class="btn btn-danger"
                                                onclick="return confirm('Yakin ingin melakukan migration? Tindakan ini tidak dapat dibatalkan!');">
                                            <i class="bi bi-arrow-repeat me-1"></i>Execute Migration (<?php echo count($preview_data); ?>+ records)
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </form>

                                <!-- Preview Table -->
                                <?php if (!empty($preview_data)): ?>
                                <hr>
                                <h6>Preview Migration (Showing first 50 records)</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Nama</th>
                                                <th>Jenjang</th>
                                                <th>Nomor Lama</th>
                                                <th>Nomor Baru</th>
                                                <th>Tanggal Daftar</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($preview_data as $data): ?>
                                            <tr>
                                                <td><?php echo $data['id']; ?></td>
                                                <td><?php echo htmlspecialchars($data['name']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $data['education_level'] == 'SMP' ? 'primary' : 'secondary'; ?>">
                                                        <?php echo $data['education_level']; ?>
                                                    </span>
                                                </td>
                                                <td><span class="text-danger fw-bold"><?php echo $data['old_number']; ?></span></td>
                                                <td><span class="text-success fw-bold"><?php echo $data['new_number']; ?></span></td>
                                                <td><?php echo date('d/m/Y', strtotime($data['created_at'])); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Migrations -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Migrations</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_migrations)): ?>
                                <?php foreach ($recent_migrations as $migration): ?>
                                <div class="border-bottom pb-2 mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span class="badge bg-secondary"><?php echo $migration['old_year']; ?></span>
                                        <i class="bi bi-arrow-right"></i>
                                        <span class="badge bg-primary"><?php echo $migration['new_year']; ?></span>
                                    </div>
                                    <small class="text-muted d-block">
                                        <?php echo date('d/m/Y H:i', strtotime($migration['transition_date'])); ?>
                                        <br><?php echo $migration['affected_registrations']; ?> records
                                    </small>
                                </div>
                                <?php endforeach; ?>
                                <?php else: ?>
                                <p class="text-muted">Belum ada migration yang dilakukan.</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-tools me-2"></i>Tools</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="test_registration_format.php" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-bug me-1"></i>Test Registration Format
                                    </a>
                                    <a href="academic_year_settings.php" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-gear me-1"></i>Academic Year Settings
                                    </a>
                                    <button class="btn btn-outline-warning btn-sm" onclick="window.print()">
                                        <i class="bi bi-printer me-1"></i>Print Report
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
