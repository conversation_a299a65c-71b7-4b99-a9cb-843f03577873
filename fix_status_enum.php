<?php
// <PERSON>ript untuk memperbaiki status enum dan memverifikasi data
require_once "config.php";

echo "<h2>🔧 Fix Status Enum Script</h2>";

// Step 1: Check current enum values
echo "<h3>1. Current Status Enum Check</h3>";
$check_enum_query = "SHOW COLUMNS FROM applicants LIKE 'status'";
$enum_result = mysqli_query($conn, $check_enum_query);

if($enum_result) {
    $enum_info = mysqli_fetch_assoc($enum_result);
    echo "Current enum: " . $enum_info['Type'] . "<br>";
    
    // Check if 'mengundurkan_diri' exists
    if(strpos($enum_info['Type'], 'mengundurkan_diri') !== false) {
        echo "✅ Status 'mengundurkan_diri' already exists<br>";
    } else {
        echo "❌ Status 'mengundurkan_diri' not found. Need to update enum.<br>";
        
        // Update enum
        echo "<h3>2. Updating Status Enum</h3>";
        $update_enum_query = "ALTER TABLE applicants 
                             MODIFY COLUMN status ENUM('pending', 'completed', 'mengundurkan_diri') DEFAULT 'pending'";
        
        if(mysqli_query($conn, $update_enum_query)) {
            echo "✅ Status enum updated successfully<br>";
            
            // Update existing 'rejected' values to 'mengundurkan_diri'
            $update_data_query = "UPDATE applicants 
                                 SET status = 'mengundurkan_diri' 
                                 WHERE status = 'rejected'";
            
            if(mysqli_query($conn, $update_data_query)) {
                echo "✅ Existing 'rejected' status updated to 'mengundurkan_diri'<br>";
            } else {
                echo "❌ Error updating existing data: " . mysqli_error($conn) . "<br>";
            }
        } else {
            echo "❌ Error updating enum: " . mysqli_error($conn) . "<br>";
        }
    }
} else {
    echo "❌ Error checking enum: " . mysqli_error($conn) . "<br>";
}

// Step 3: Show current status distribution
echo "<h3>3. Current Status Distribution</h3>";
$status_query = "SELECT status, COUNT(*) as count FROM applicants GROUP BY status";
$status_result = mysqli_query($conn, $status_query);

if($status_result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Status</th><th>Count</th></tr>";
    
    while($status_row = mysqli_fetch_assoc($status_result)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($status_row['status']) . "</td>";
        echo "<td>" . $status_row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error getting status distribution: " . mysqli_error($conn) . "<br>";
}

// Step 4: Test the group comparison query
echo "<h3>4. Test Group Comparison Query</h3>";
$test_query = "SELECT g.id, g.name,
               COUNT(a.id) as total_count,
               COUNT(CASE WHEN a.education_level = 'SMP' THEN 1 END) as smp_count,
               COUNT(CASE WHEN a.education_level = 'SMA' THEN 1 END) as sma_count,
               COUNT(CASE WHEN (SELECT COUNT(*) FROM documents WHERE applicant_id = a.id) = 11 THEN 1 END) as completed_count,
               COUNT(CASE WHEN a.status = 'pending' THEN 1 END) as pending_count,
               COUNT(CASE WHEN a.status = 'mengundurkan_diri' THEN 1 END) as withdrawn_count
               FROM groups g 
               LEFT JOIN applicants a ON g.id = a.group_id 
               GROUP BY g.id, g.name 
               ORDER BY g.id";

$test_result = mysqli_query($conn, $test_query);

if($test_result) {
    echo "✅ Group comparison query works!<br>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Group</th><th>Total</th><th>SMP</th><th>SMA</th><th>Docs Complete</th><th>Pending</th><th>Withdrawn</th></tr>";
    
    while($group_row = mysqli_fetch_assoc($test_result)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($group_row['name']) . "</td>";
        echo "<td>" . $group_row['total_count'] . "</td>";
        echo "<td>" . $group_row['smp_count'] . "</td>";
        echo "<td>" . $group_row['sma_count'] . "</td>";
        echo "<td>" . $group_row['completed_count'] . "</td>";
        echo "<td>" . $group_row['pending_count'] . "</td>";
        echo "<td>" . $group_row['withdrawn_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Error testing group comparison query: " . mysqli_error($conn) . "<br>";
}

// Step 5: Add some test data with 'mengundurkan_diri' status if none exists
echo "<h3>5. Test Data Check</h3>";
$withdrawn_check = "SELECT COUNT(*) as count FROM applicants WHERE status = 'mengundurkan_diri'";
$withdrawn_result = mysqli_query($conn, $withdrawn_check);
$withdrawn_count = mysqli_fetch_assoc($withdrawn_result)['count'];

if($withdrawn_count == 0) {
    echo "No 'mengundurkan_diri' data found. Would you like to add test data?<br>";
    echo "<form method='post'>";
    echo "<button type='submit' name='add_test_data' class='btn btn-warning'>Add Test Data</button>";
    echo "</form>";
    
    if(isset($_POST['add_test_data'])) {
        // Find a pending applicant to change status
        $find_pending = "SELECT id FROM applicants WHERE status = 'pending' LIMIT 1";
        $pending_result = mysqli_query($conn, $find_pending);
        
        if($pending_row = mysqli_fetch_assoc($pending_result)) {
            $update_test = "UPDATE applicants SET status = 'mengundurkan_diri' WHERE id = " . $pending_row['id'];
            if(mysqli_query($conn, $update_test)) {
                echo "✅ Test data added successfully!<br>";
                echo "<script>window.location.reload();</script>";
            } else {
                echo "❌ Error adding test data: " . mysqli_error($conn) . "<br>";
            }
        } else {
            echo "❌ No pending applicants found to convert<br>";
        }
    }
} else {
    echo "✅ Found $withdrawn_count applicants with 'mengundurkan_diri' status<br>";
}

echo "<h3>🎯 Summary</h3>";
echo "<p>If all checks show ✅, then the status counting should work correctly in group_comparison.php</p>";

echo "<h3>🔗 Test Links</h3>";
echo "<ul>";
echo "<li><a href='group_comparison.php'>Test Group Comparison Page</a></li>";
echo "<li><a href='test_group_features.php'>Run Full Feature Test</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}
h2, h3 {
    color: #333;
}
table {
    background-color: white;
    padding: 10px;
}
th {
    background-color: #0d6efd;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
.btn-warning {
    background-color: #ffc107;
    color: #000;
}
</style>
