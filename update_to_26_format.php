<?php
// Script untuk mengubah semua nomor pendaftaran dari P25/A25 menjadi P26/A26
require_once "config.php";

// Check if user is logged in and is admin
require_admin();

$update_success = false;
$update_count = 0;
$error_message = "";
$preview_data = [];

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST["preview"])) {
        // Preview mode - show what will be changed
        $sql = "SELECT id, registration_number, name, education_level FROM applicants WHERE registration_number LIKE 'P25.%' OR registration_number LIKE 'A25.%' ORDER BY registration_number";
        $result = mysqli_query($conn, $sql);
        
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $old_number = $row['registration_number'];
                $new_number = str_replace(['P25.', 'A25.'], ['P26.', 'A26.'], $old_number);
                
                $preview_data[] = [
                    'id' => $row['id'],
                    'name' => $row['name'],
                    'education_level' => $row['education_level'],
                    'old_number' => $old_number,
                    'new_number' => $new_number
                ];
            }
        }
    } elseif (isset($_POST["update_all"])) {
        // Actual update
        mysqli_begin_transaction($conn);
        
        try {
            // Get all applicants with P25/A25 format
            $sql = "SELECT id, registration_number FROM applicants WHERE registration_number LIKE 'P25.%' OR registration_number LIKE 'A25.%'";
            $result = mysqli_query($conn, $sql);
            
            if ($result) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $id = $row['id'];
                    $old_number = $row['registration_number'];
                    $new_number = str_replace(['P25.', 'A25.'], ['P26.', 'A26.'], $old_number);
                    
                    // Update the registration number
                    $update_sql = "UPDATE applicants SET registration_number = ? WHERE id = ?";
                    $stmt = mysqli_prepare($conn, $update_sql);
                    mysqli_stmt_bind_param($stmt, "si", $new_number, $id);
                    
                    if (mysqli_stmt_execute($stmt)) {
                        $update_count++;
                        
                        // Log the change
                        $log_sql = "INSERT INTO registration_number_logs (applicant_id, old_number, new_number, changed_by) VALUES (?, ?, ?, ?)";
                        $log_stmt = mysqli_prepare($conn, $log_sql);
                        if ($log_stmt) {
                            $user_id = $_SESSION['user_id'];
                            mysqli_stmt_bind_param($log_stmt, "issi", $id, $old_number, $new_number, $user_id);
                            mysqli_stmt_execute($log_stmt);
                            mysqli_stmt_close($log_stmt);
                        }
                    }
                    
                    mysqli_stmt_close($stmt);
                }
                
                mysqli_commit($conn);
                $update_success = true;
            }
        } catch (Exception $e) {
            mysqli_rollback($conn);
            $error_message = "Error updating registration numbers: " . $e->getMessage();
        }
    }
}

// Get current statistics
$stats_25 = 0;
$stats_26 = 0;

$sql_25 = "SELECT COUNT(*) as count FROM applicants WHERE registration_number LIKE 'P25.%' OR registration_number LIKE 'A25.%'";
$result_25 = mysqli_query($conn, $sql_25);
if ($row_25 = mysqli_fetch_assoc($result_25)) {
    $stats_25 = $row_25['count'];
}

$sql_26 = "SELECT COUNT(*) as count FROM applicants WHERE registration_number LIKE 'P26.%' OR registration_number LIKE 'A26.%'";
$result_26 = mysqli_query($conn, $sql_26);
if ($row_26 = mysqli_fetch_assoc($result_26)) {
    $stats_26 = $row_26['count'];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Format Nomor Pendaftaran ke P26/A26</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-arrow-repeat me-2"></i>
                            Update Format Nomor Pendaftaran ke P26/A26
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($update_success): ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>Berhasil!</strong> <?php echo $update_count; ?> nomor pendaftaran telah diupdate ke format P26/A26.
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>Error!</strong> <?php echo $error_message; ?>
                        </div>
                        <?php endif; ?>

                        <!-- Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats_25; ?></h3>
                                        <p class="mb-0">Nomor dengan format P25/A25</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats_26; ?></h3>
                                        <p class="mb-0">Nomor dengan format P26/A26</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Information -->
                        <div class="alert alert-info">
                            <h5><i class="bi bi-info-circle-fill me-2"></i>Informasi</h5>
                            <p>Script ini akan mengubah semua nomor pendaftaran dari format lama (P25/A25) menjadi format baru (P26/A26) untuk tahun pelajaran 2026.</p>
                            <ul>
                                <li><strong>P25.2.001</strong> → <strong>P26.2.001</strong></li>
                                <li><strong>A25.2.001</strong> → <strong>A26.2.001</strong></li>
                            </ul>
                        </div>

                        <?php if ($stats_25 > 0): ?>
                        <!-- Preview/Update Form -->
                        <form method="post">
                            <div class="d-flex gap-2 mb-3">
                                <button type="submit" name="preview" class="btn btn-outline-primary">
                                    <i class="bi bi-eye me-2"></i>Preview Perubahan
                                </button>
                                
                                <?php if (!empty($preview_data)): ?>
                                <button type="submit" name="update_all" class="btn btn-danger" 
                                        onclick="return confirm('Apakah Anda yakin ingin mengubah <?php echo count($preview_data); ?> nomor pendaftaran? Tindakan ini tidak dapat dibatalkan!');">
                                    <i class="bi bi-arrow-repeat me-2"></i>Update Semua (<?php echo count($preview_data); ?> data)
                                </button>
                                <?php endif; ?>
                            </div>
                        </form>

                        <!-- Preview Table -->
                        <?php if (!empty($preview_data)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Nama</th>
                                        <th>Jenjang</th>
                                        <th>Nomor Lama</th>
                                        <th>Nomor Baru</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($preview_data as $data): ?>
                                    <tr>
                                        <td><?php echo $data['id']; ?></td>
                                        <td><?php echo htmlspecialchars($data['name']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $data['education_level'] == 'SMP' ? 'primary' : 'secondary'; ?>">
                                                <?php echo $data['education_level']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-danger fw-bold"><?php echo $data['old_number']; ?></span>
                                        </td>
                                        <td>
                                            <span class="text-success fw-bold"><?php echo $data['new_number']; ?></span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>

                        <?php else: ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <strong>Selesai!</strong> Semua nomor pendaftaran sudah menggunakan format P26/A26.
                        </div>
                        <?php endif; ?>

                        <!-- Back Button -->
                        <div class="mt-4">
                            <a href="admin_dashboard.php" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Kembali ke Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
