<?php
// Database configuration
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'db_seminary');

// Attempt to connect to MySQL database
$conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

// Check connection
if($conn === false){
    die("ERROR: Could not connect to database. " . mysqli_connect_error());
}

// Set charset to ensure proper handling of special characters
mysqli_set_charset($conn, "utf8mb4");

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Function to sanitize input data
function sanitize_input($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    $data = mysqli_real_escape_string($conn, $data);
    return $data;
}

// Function to get current academic year from settings
function get_current_academic_year() {
    global $conn;

    // Try to get from database settings
    $sql = "SELECT setting_value FROM academic_year_settings WHERE setting_name = 'current_academic_year' AND is_active = TRUE LIMIT 1";
    $result = mysqli_query($conn, $sql);

    if ($result && $row = mysqli_fetch_assoc($result)) {
        return $row['setting_value'];
    }

    // Fallback: auto-calculate based on current date if auto mode
    $mode_sql = "SELECT setting_value FROM academic_year_settings WHERE setting_name = 'academic_year_mode' AND is_active = TRUE LIMIT 1";
    $mode_result = mysqli_query($conn, $mode_sql);

    if ($mode_result && $mode_row = mysqli_fetch_assoc($mode_result)) {
        if ($mode_row['setting_value'] === 'auto') {
            return calculate_auto_academic_year();
        }
    }

    // Final fallback
    return "26";
}

// Function to calculate academic year automatically based on date
function calculate_auto_academic_year() {
    global $conn;

    // Get auto year settings
    $start_month = 7; // Default July
    $start_day = 1;   // Default 1st

    $month_sql = "SELECT setting_value FROM academic_year_settings WHERE setting_name = 'auto_year_start_month' AND is_active = TRUE LIMIT 1";
    $month_result = mysqli_query($conn, $month_sql);
    if ($month_result && $month_row = mysqli_fetch_assoc($month_result)) {
        $start_month = intval($month_row['setting_value']);
    }

    $day_sql = "SELECT setting_value FROM academic_year_settings WHERE setting_name = 'auto_year_start_day' AND is_active = TRUE LIMIT 1";
    $day_result = mysqli_query($conn, $day_sql);
    if ($day_result && $day_row = mysqli_fetch_assoc($day_result)) {
        $start_day = intval($day_row['setting_value']);
    }

    // Calculate academic year based on current date
    $current_date = new DateTime();
    $current_year = intval($current_date->format('Y'));
    $academic_start = new DateTime($current_year . '-' . str_pad($start_month, 2, '0', STR_PAD_LEFT) . '-' . str_pad($start_day, 2, '0', STR_PAD_LEFT));

    // If current date is before academic year start, use previous year
    if ($current_date < $academic_start) {
        $academic_year = $current_year;
    } else {
        $academic_year = $current_year + 1;
    }

    // Return last 2 digits
    return substr(strval($academic_year), -2);
}

// Function to get current wave from settings
function get_current_wave() {
    global $conn;

    $sql = "SELECT setting_value FROM academic_year_settings WHERE setting_name = 'current_wave' AND is_active = TRUE LIMIT 1";
    $result = mysqli_query($conn, $sql);

    if ($result && $row = mysqli_fetch_assoc($result)) {
        return intval($row['setting_value']);
    }

    return 2; // Default wave
}

// Function to generate registration number with wave number
function generate_registration_number($education_level, $wave = null) {
    global $conn;

    // Get current academic year from settings
    $year = get_current_academic_year();

    // Get current wave if not specified
    if ($wave === null) {
        $wave = get_current_wave();
    }

    $prefix = ($education_level == 'SMP') ? "P" : "A";

    // Get the latest registration number for this education level, year and wave
    $sql = "SELECT MAX(registration_number) as max_reg FROM applicants WHERE registration_number LIKE '{$prefix}{$year}.{$wave}.%'";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);

    if ($row['max_reg']) {
        $last_number = intval(substr($row['max_reg'], -3));
        $next_number = $last_number + 1;
    } else {
        $next_number = 1;
    }

    // Format: P26.2.001 or A26.2.001
    return $prefix . $year . "." . $wave . "." . str_pad($next_number, 3, '0', STR_PAD_LEFT);
}

// Function to extract wave number from registration number
function get_wave_from_registration_number($registration_number) {
    if (preg_match('/^\w\d{2}\.(\d+)\./', $registration_number, $matches)) {
        return $matches[1];
    }
    return 2; // Default to wave 2 if not found
}

// Function to check if user is logged in
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

// Function to check if user is admin
function is_admin() {
    return isset($_SESSION['role']) && $_SESSION['role'] == 'admin';
}

// Function to check if user is group leader
function is_group_leader() {
    return isset($_SESSION['role']) && $_SESSION['role'] == 'group_leader';
}

// Function to check if user is interviewer
function is_interviewer() {
    return isset($_SESSION['role']) && $_SESSION['role'] == 'interviewer';
}

// Function to check if user is admin interviewer
function is_admin_interviewer() {
    return is_interviewer() && isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

// Function to check if user is group interviewer (bidang 4)
function is_group_interviewer() {
    return is_interviewer() && isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === false;
}

// Function to get interviewer section ID
function get_interviewer_section_id() {
    return isset($_SESSION['section_id']) ? $_SESSION['section_id'] : 0;
}

// Function to get interviewer group ID
function get_interviewer_group_id() {
    return isset($_SESSION['group_id']) ? $_SESSION['group_id'] : 0;
}

// Redirect if not logged in
function require_login() {
    if (!is_logged_in()) {
        header("Location: login.php");
        exit;
    }
}

// Redirect if not admin
function require_admin() {
    require_login();
    if (!is_admin()) {
        header("Location: index.php");
        exit;
    }
}

// Redirect if not interviewer
function require_interviewer() {
    require_login();
    if (!is_interviewer()) {
        header("Location: index.php");
        exit;
    }
}

// Redirect if not admin interviewer
function require_admin_interviewer() {
    require_login();
    if (!is_admin_interviewer()) {
        header("Location: index.php");
        exit;
    }
}

// Redirect if not group interviewer (bidang 4)
function require_group_interviewer() {
    require_login();
    if (!is_group_interviewer()) {
        header("Location: index.php");
        exit;
    }
}

// Check if user is interviewer for section 4 (Kepribadian/Seksualitas)
function is_section4_interviewer() {
    return is_interviewer() && get_interviewer_section_id() == 4;
}

// Redirect if not section 4 interviewer
function require_section4_interviewer() {
    require_login();
    if (!is_section4_interviewer()) {
        header("Location: index.php");
        exit;
    }
}

// Get the document status for an applicant
function get_document_status($applicant_id) {
    global $conn;

    // Get cache
    $cache = get_cache();

    // Try to get from cache first
    $cacheKey = 'document_status_' . $applicant_id;
    $cachedStatus = $cache->getCache($cacheKey, 300); // Cache for 5 minutes

    if ($cachedStatus !== false) {
        return $cachedStatus;
    }

    // Get total required documents
    $total_required = 11; // Updated from 10 to 11 (added parent permission letter)

    // Coba gunakan stored procedure jika tersedia
    $use_stored_procedure = false;

    try {
        // Cek apakah stored procedure tersedia
        $check_sp = "SHOW PROCEDURE STATUS WHERE Db = DATABASE() AND Name = 'GetApplicantDocumentStatus'";
        $sp_result = mysqli_query($conn, $check_sp);

        if ($sp_result && mysqli_num_rows($sp_result) > 0) {
            $use_stored_procedure = true;
        }
    } catch (Exception $e) {
        // Jika error, gunakan query biasa
        $use_stored_procedure = false;
    }

    if ($use_stored_procedure) {
        // Gunakan stored procedure
        $sql = "CALL GetApplicantDocumentStatus(?)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $applicant_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        $uploaded = $row['uploaded_docs'];

        // Free result
        mysqli_free_result($result);

        // Jika ada multiple result sets, clear them
        while (mysqli_more_results($conn) && mysqli_next_result($conn)) {
            if ($result = mysqli_store_result($conn)) {
                mysqli_free_result($result);
            }
        }
    } else {
        // Coba gunakan tabel statistik jika tersedia
        $sql = "SELECT doc_count as uploaded FROM applicant_stats WHERE applicant_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $applicant_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        // Jika tabel statistik ada dan memiliki data
        if (mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            $uploaded = $row['uploaded'];
        } else {
            // Fallback ke query langsung jika tabel statistik tidak tersedia
            $sql = "SELECT COUNT(*) as uploaded FROM documents WHERE applicant_id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "i", $applicant_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            $uploaded = $row['uploaded'];
        }
    }

    $status = "$uploaded/$total_required";

    // Save to cache
    $cache->setCache($cacheKey, $status, 300); // Cache for 5 minutes

    return $status;
}

// Format registration number for display
function format_registration_number($registration_number) {
    return '<span class="fw-bold">' . $registration_number . '</span>';
}

// Get the interview status for an applicant
function get_interview_status($applicant_id) {
    global $conn;

    // Get cache
    $cache = get_cache();

    // Try to get from cache first
    $cacheKey = 'interview_status_' . $applicant_id;
    $cachedStatus = $cache->getCache($cacheKey, 300); // Cache for 5 minutes

    if ($cachedStatus !== false) {
        return $cachedStatus;
    }

    // Coba gunakan tabel statistik jika tersedia
    $sql = "SELECT completed_interviews, total_interviews FROM applicant_stats WHERE applicant_id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $applicant_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    // Jika tabel statistik ada dan memiliki data
    if (mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $completed = $row['completed_interviews'];
        $total = $row['total_interviews'];
    } else {
        // Fallback ke query langsung jika tabel statistik tidak tersedia
        $sql = "SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                FROM interviews
                WHERE applicant_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $applicant_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        $completed = $row['completed'];
        $total = $row['total'];
    }

    $status = "$completed/$total";

    // Save to cache
    $cache->setCache($cacheKey, $status, 300); // Cache for 5 minutes

    return $status;
}

// Initialize cache if not already initialized
function get_cache() {
    static $cache = null;

    if ($cache === null) {
        require_once __DIR__ . '/cache_helper.php';
        $cache = new CacheHelper();

        // Set cache status from session if available
        if (isset($_SESSION['cache_enabled'])) {
            $cache->setEnabled($_SESSION['cache_enabled']);
        }

        // Set debug mode from session if available
        if (isset($_SESSION['cache_debug'])) {
            $cache->setDebugMode($_SESSION['cache_debug']);
        }
    }

    return $cache;
}

// Initialize cache invalidator if not already initialized
function get_cache_invalidator() {
    static $invalidator = null;

    if ($invalidator === null) {
        global $conn;
        require_once __DIR__ . '/cache_helper.php';
        require_once __DIR__ . '/cache_invalidator.php';
        $cache = get_cache();
        $invalidator = new CacheInvalidator($cache, $conn);
    }

    return $invalidator;
}

// Invalidate cache after data update
function invalidate_cache($type, $id = null) {
    $invalidator = get_cache_invalidator();
    $invalidator->invalidate($type, $id);
}