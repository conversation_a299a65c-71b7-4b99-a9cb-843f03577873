<?php
// Include config file
require_once "config.php";

// Check if user is logged in and is admin
require_admin();

$success_message = "";
$error_message = "";

// Create tables if they don't exist
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'academic_year_settings'");
if(mysqli_num_rows($check_table) == 0) {
    // Run the SQL script to create tables
    $sql_file = file_get_contents('create_academic_year_settings.sql');
    if ($sql_file) {
        // Execute SQL commands (simplified version)
        $commands = explode(';', $sql_file);
        foreach ($commands as $command) {
            $command = trim($command);
            if (!empty($command) && !preg_match('/^(DELIMITER|CREATE FUNCTION|CREATE PROCEDURE)/i', $command)) {
                mysqli_query($conn, $command);
            }
        }
    }
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST["update_settings"])) {
        $current_year = trim($_POST["current_academic_year"]);
        $year_mode = trim($_POST["academic_year_mode"]);
        $current_wave = intval($_POST["current_wave"]);
        $auto_start_month = intval($_POST["auto_year_start_month"]);
        $auto_start_day = intval($_POST["auto_year_start_day"]);
        $registration_active = isset($_POST["registration_active"]) ? 'true' : 'false';
        
        mysqli_begin_transaction($conn);
        
        try {
            // Update settings
            $settings = [
                'current_academic_year' => $current_year,
                'academic_year_mode' => $year_mode,
                'current_wave' => $current_wave,
                'auto_year_start_month' => $auto_start_month,
                'auto_year_start_day' => $auto_start_day,
                'registration_active' => $registration_active
            ];
            
            foreach ($settings as $name => $value) {
                $update_sql = "UPDATE academic_year_settings SET setting_value = ?, updated_by = ? WHERE setting_name = ?";
                $stmt = mysqli_prepare($conn, $update_sql);
                mysqli_stmt_bind_param($stmt, "sis", $value, $_SESSION['user_id'], $name);
                mysqli_stmt_execute($stmt);
                mysqli_stmt_close($stmt);
            }
            
            mysqli_commit($conn);
            $success_message = "Pengaturan tahun akademik berhasil diupdate!";
        } catch (Exception $e) {
            mysqli_rollback($conn);
            $error_message = "Error updating settings: " . $e->getMessage();
        }
    } elseif (isset($_POST["transition_year"])) {
        $new_year = trim($_POST["new_year"]);
        $transition_notes = trim($_POST["transition_notes"]);
        
        if (!empty($new_year) && preg_match('/^\d{2}$/', $new_year)) {
            mysqli_begin_transaction($conn);
            
            try {
                // Get old year
                $old_year_sql = "SELECT setting_value FROM academic_year_settings WHERE setting_name = 'current_academic_year'";
                $old_year_result = mysqli_query($conn, $old_year_sql);
                $old_year = mysqli_fetch_assoc($old_year_result)['setting_value'];
                
                // Update year
                $update_sql = "UPDATE academic_year_settings SET setting_value = ?, updated_by = ? WHERE setting_name = 'current_academic_year'";
                $stmt = mysqli_prepare($conn, $update_sql);
                mysqli_stmt_bind_param($stmt, "si", $new_year, $_SESSION['user_id']);
                mysqli_stmt_execute($stmt);
                mysqli_stmt_close($stmt);
                
                // Log transition
                $log_sql = "INSERT INTO academic_year_history (old_year, new_year, transition_type, changed_by, notes) VALUES (?, ?, 'manual', ?, ?)";
                $log_stmt = mysqli_prepare($conn, $log_sql);
                mysqli_stmt_bind_param($log_stmt, "ssis", $old_year, $new_year, $_SESSION['user_id'], $transition_notes);
                mysqli_stmt_execute($log_stmt);
                mysqli_stmt_close($log_stmt);
                
                mysqli_commit($conn);
                $success_message = "Transisi tahun akademik dari $old_year ke $new_year berhasil!";
            } catch (Exception $e) {
                mysqli_rollback($conn);
                $error_message = "Error transitioning year: " . $e->getMessage();
            }
        } else {
            $error_message = "Format tahun tidak valid. Gunakan format 2 digit (contoh: 27)";
        }
    }
}

// Get current settings
$settings = [];
$settings_sql = "SELECT setting_name, setting_value, description, updated_at FROM academic_year_settings WHERE is_active = TRUE";
$settings_result = mysqli_query($conn, $settings_sql);
while ($row = mysqli_fetch_assoc($settings_result)) {
    $settings[$row['setting_name']] = $row;
}

// Get transition history
$history = [];
$history_sql = "SELECT h.*, u.name as changed_by_name FROM academic_year_history h 
                LEFT JOIN users u ON h.changed_by = u.id 
                ORDER BY h.transition_date DESC LIMIT 10";
$history_result = mysqli_query($conn, $history_sql);
while ($row = mysqli_fetch_assoc($history_result)) {
    $history[] = $row;
}

// Calculate auto year for preview
$auto_year = calculate_auto_academic_year();

// Get registration statistics
$stats = [];
$current_year = $settings['current_academic_year']['setting_value'] ?? '26';
$stats_sql = "SELECT 
              COUNT(*) as total,
              COUNT(CASE WHEN registration_number LIKE 'P{$current_year}.%' THEN 1 END) as smp_current,
              COUNT(CASE WHEN registration_number LIKE 'A{$current_year}.%' THEN 1 END) as sma_current,
              COUNT(CASE WHEN registration_number NOT LIKE 'P{$current_year}.%' AND registration_number NOT LIKE 'A{$current_year}.%' THEN 1 END) as other_years
              FROM applicants";
$stats_result = mysqli_query($conn, $stats_sql);
$stats = mysqli_fetch_assoc($stats_result);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengaturan Tahun Akademik - Seminari Menengah St. Petrus Canisius</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-calendar-event me-2"></i>Pengaturan Tahun Akademik</h2>
                    <a href="admin_dashboard.php" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Kembali ke Dashboard
                    </a>
                </div>

                <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle-fill me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i><?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Current Status -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $current_year; ?></h3>
                                <p class="mb-0">Tahun Akademik Saat Ini</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $settings['current_wave']['setting_value'] ?? '2'; ?></h3>
                                <p class="mb-0">Gelombang Saat Ini</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $stats['smp_current'] + $stats['sma_current']; ?></h3>
                                <p class="mb-0">Pendaftar Tahun Ini</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h3><?php echo $auto_year; ?></h3>
                                <p class="mb-0">Auto-Calculate Year</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Settings Form -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-gear-fill me-2"></i>Pengaturan Umum</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Tahun Akademik Saat Ini</label>
                                                <input type="text" name="current_academic_year" class="form-control" 
                                                       value="<?php echo $settings['current_academic_year']['setting_value'] ?? '26'; ?>"
                                                       pattern="\d{2}" maxlength="2" required>
                                                <div class="form-text">Format 2 digit (contoh: 26 untuk 2026)</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Mode Pengaturan Tahun</label>
                                                <select name="academic_year_mode" class="form-select" id="yearMode">
                                                    <option value="manual" <?php echo ($settings['academic_year_mode']['setting_value'] ?? 'manual') == 'manual' ? 'selected' : ''; ?>>Manual</option>
                                                    <option value="auto" <?php echo ($settings['academic_year_mode']['setting_value'] ?? 'manual') == 'auto' ? 'selected' : ''; ?>>Otomatis</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Gelombang Saat Ini</label>
                                                <select name="current_wave" class="form-select">
                                                    <?php for($i = 1; $i <= 9; $i++): ?>
                                                    <option value="<?php echo $i; ?>" <?php echo ($settings['current_wave']['setting_value'] ?? '2') == $i ? 'selected' : ''; ?>>
                                                        Gelombang <?php echo $i; ?>
                                                    </option>
                                                    <?php endfor; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Status Pendaftaran</label>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" name="registration_active" 
                                                           <?php echo ($settings['registration_active']['setting_value'] ?? 'true') == 'true' ? 'checked' : ''; ?>>
                                                    <label class="form-check-label">Pendaftaran Aktif</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="autoSettings" style="display: none;">
                                        <hr>
                                        <h6><i class="bi bi-calendar-check me-2"></i>Pengaturan Mode Otomatis</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Bulan Mulai Tahun Akademik</label>
                                                    <select name="auto_year_start_month" class="form-select">
                                                        <?php 
                                                        $months = ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 
                                                                  'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
                                                        for($i = 1; $i <= 12; $i++): 
                                                        ?>
                                                        <option value="<?php echo $i; ?>" <?php echo ($settings['auto_year_start_month']['setting_value'] ?? '7') == $i ? 'selected' : ''; ?>>
                                                            <?php echo $months[$i-1]; ?>
                                                        </option>
                                                        <?php endfor; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label class="form-label">Tanggal Mulai</label>
                                                    <input type="number" name="auto_year_start_day" class="form-control" 
                                                           value="<?php echo $settings['auto_year_start_day']['setting_value'] ?? '1'; ?>"
                                                           min="1" max="31">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="alert alert-info">
                                            <small><i class="bi bi-info-circle me-1"></i>
                                            Mode otomatis akan menghitung tahun akademik berdasarkan tanggal saat ini. 
                                            Tahun akademik yang dihitung otomatis saat ini: <strong><?php echo $auto_year; ?></strong>
                                            </small>
                                        </div>
                                    </div>

                                    <button type="submit" name="update_settings" class="btn btn-primary">
                                        <i class="bi bi-check-lg me-2"></i>Simpan Pengaturan
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-lightning-fill me-2"></i>Aksi Cepat</h5>
                            </div>
                            <div class="card-body">
                                <!-- Year Transition -->
                                <form method="post" class="mb-3">
                                    <h6>Transisi Tahun Akademik</h6>
                                    <div class="mb-2">
                                        <input type="text" name="new_year" class="form-control" 
                                               placeholder="Tahun baru (contoh: 27)" pattern="\d{2}" maxlength="2" required>
                                    </div>
                                    <div class="mb-2">
                                        <textarea name="transition_notes" class="form-control" rows="2" 
                                                  placeholder="Catatan transisi (opsional)"></textarea>
                                    </div>
                                    <button type="submit" name="transition_year" class="btn btn-warning btn-sm w-100"
                                            onclick="return confirm('Yakin ingin melakukan transisi tahun akademik?');">
                                        <i class="bi bi-arrow-right-circle me-1"></i>Transisi Tahun
                                    </button>
                                </form>

                                <hr>

                                <!-- Quick Links -->
                                <h6>Tools Terkait</h6>
                                <div class="d-grid gap-2">
                                    <a href="test_registration_format.php" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-bug me-1"></i>Test Format Nomor
                                    </a>
                                    <a href="year_migration_tools.php" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-arrow-repeat me-1"></i>Migration Tools
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- History Section -->
                <?php if (!empty($history)): ?>
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Riwayat Transisi Tahun Akademik</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Tanggal</th>
                                                <th>Dari</th>
                                                <th>Ke</th>
                                                <th>Tipe</th>
                                                <th>Oleh</th>
                                                <th>Catatan</th>
                                                <th>Affected</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($history as $h): ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y H:i', strtotime($h['transition_date'])); ?></td>
                                                <td><span class="badge bg-secondary"><?php echo $h['old_year']; ?></span></td>
                                                <td><span class="badge bg-primary"><?php echo $h['new_year']; ?></span></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $h['transition_type'] == 'manual' ? 'warning' : 'info'; ?>">
                                                        <?php echo ucfirst($h['transition_type']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($h['changed_by_name'] ?? 'System'); ?></td>
                                                <td><?php echo htmlspecialchars($h['notes'] ?? '-'); ?></td>
                                                <td><?php echo $h['affected_registrations']; ?> records</td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show/hide auto settings based on mode
        document.getElementById('yearMode').addEventListener('change', function() {
            const autoSettings = document.getElementById('autoSettings');
            if (this.value === 'auto') {
                autoSettings.style.display = 'block';
            } else {
                autoSettings.style.display = 'none';
            }
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            const yearMode = document.getElementById('yearMode');
            if (yearMode.value === 'auto') {
                document.getElementById('autoSettings').style.display = 'block';
            }
        });
    </script>
</body>
</html>
