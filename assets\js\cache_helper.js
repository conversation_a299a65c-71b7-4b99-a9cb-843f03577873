/**
 * Cache Helper JavaScript
 * Membantu mengatasi masalah cache browser untuk gambar
 */

// Fungsi untuk memuat ulang gambar dengan parameter cache-busting
function reloadImage(imgElement) {
    const currentSrc = imgElement.src;
    const newSrc = addCacheBustingParam(currentSrc);
    imgElement.src = newSrc;
}

// Fungsi untuk menambahkan parameter cache-busting ke URL
function addCacheBustingParam(url) {
    // Hapus parameter v= yang mungkin sudah ada
    let cleanUrl = url.split('?')[0];
    
    // Tambahkan parameter v= dengan timestamp baru
    return cleanUrl + '?v=' + new Date().getTime();
}

// Fungsi untuk memuat ulang semua gambar dengan class tertentu
function reloadAllImages(className = 'profile-pic') {
    const images = document.querySelectorAll('.' + className);
    images.forEach(img => {
        reloadImage(img);
    });
}

// Tambahkan event listener untuk tombol refresh foto jika ada
document.addEventListener('DOMContentLoaded', function() {
    const refreshButtons = document.querySelectorAll('.refresh-photo-btn');
    refreshButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('data-target');
            const imgElement = document.getElementById(targetId);
            if (imgElement) {
                reloadImage(imgElement);
            }
        });
    });
});
