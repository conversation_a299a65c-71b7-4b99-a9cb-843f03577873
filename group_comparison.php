<?php
// Include config file
require_once "config.php";

// Check if user is logged in and is admin or group leader
require_login();
if(!is_admin() && !is_group_leader()){
    header("location: login.php");
    exit;
}

// Get all groups with statistics
$groups_query = "SELECT g.id, g.name,
                 COUNT(a.id) as total_count,
                 COUNT(CASE WHEN a.education_level = 'SMP' THEN 1 END) as smp_count,
                 COUNT(CASE WHEN a.education_level = 'SMA' THEN 1 END) as sma_count,
                 COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_count,
                 COUNT(CASE WHEN a.status = 'pending' THEN 1 END) as pending_count,
                 COUNT(CASE WHEN a.status = 'mengundurkan_diri' OR a.status = 'rejected' THEN 1 END) as withdrawn_count,
                 AVG((SELECT COUNT(*) FROM documents WHERE applicant_id = a.id)) as avg_documents,
                 AVG((SELECT COUNT(*) FROM interviews WHERE applicant_id = a.id AND status = 'completed')) as avg_interviews
                 FROM groups g 
                 LEFT JOIN applicants a ON g.id = a.group_id 
                 GROUP BY g.id, g.name 
                 ORDER BY g.id";

$groups_result = mysqli_query($conn, $groups_query);
$groups_data = [];
while ($row = mysqli_fetch_assoc($groups_result)) {
    $groups_data[] = $row;
}

// Calculate totals
$total_applicants = 0;
$total_smp = 0;
$total_sma = 0;
$total_completed = 0;
$total_pending = 0;
$total_withdrawn = 0;

foreach($groups_data as $group) {
    $total_applicants += $group['total_count'];
    $total_smp += $group['smp_count'];
    $total_sma += $group['sma_count'];
    $total_completed += $group['completed_count'];
    $total_pending += $group['pending_count'];
    $total_withdrawn += $group['withdrawn_count'];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perbandingan Kelompok - Seminari Menengah St. Petrus Canisius</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            padding: 15px 0;
            margin-bottom: 30px;
        }
        .logo img {
            width: 40px;
            height: auto;
            margin-right: 10px;
        }
        .stats-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            transition: all 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stats-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #0d6efd;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            color: #6c757d;
        }
        .comparison-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .group-row {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
            transition: background-color 0.3s;
        }
        .group-row:hover {
            background-color: #f8f9fa;
        }
        .group-row:last-child {
            border-bottom: none;
        }
        .group-name {
            font-weight: bold;
            color: #495057;
        }
        .metric-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin: 2px;
        }
        .progress-thin {
            height: 8px;
        }
        .btn-group-nav {
            margin-bottom: 20px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center logo">
                    <img src="assets/img/logo.png" alt="Logo Seminari">
                    <h5 class="mb-0">Perbandingan Kelompok</h5>
                    <p class="mb-0 ms-3 text-white-50">Seminari Menengah St. Petrus Canisius</p>
                </div>
                <div>
                    <?php if(is_admin()): ?>
                    <a href="admin_dashboard.php" class="btn btn-light btn-sm">← Dashboard Admin</a>
                    <?php else: ?>
                    <a href="group_dashboard.php" class="btn btn-light btn-sm">← Dashboard Kelompok</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Overall Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="bi bi-people-fill stats-icon"></i>
                    <div class="stats-number"><?php echo $total_applicants; ?></div>
                    <div class="stats-label">Total Pendaftar</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="bi bi-person-fill stats-icon" style="color: #0d6efd;"></i>
                    <div class="stats-number"><?php echo $total_smp; ?></div>
                    <div class="stats-label">Pendaftar SMP</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="bi bi-mortarboard-fill stats-icon" style="color: #6f42c1;"></i>
                    <div class="stats-number"><?php echo $total_sma; ?></div>
                    <div class="stats-label">Pendaftar SMA</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="bi bi-check-circle-fill stats-icon" style="color: #198754;"></i>
                    <div class="stats-number"><?php echo $total_completed; ?></div>
                    <div class="stats-label">Dokumen Lengkap</div>
                </div>
            </div>
        </div>

        <!-- Group Navigation -->
        <div class="btn-group-nav">
            <div class="d-flex flex-wrap gap-2">
                <?php foreach($groups_data as $group): ?>
                <a href="group_view.php?group_id=<?php echo $group['id']; ?>" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-eye me-1"></i><?php echo htmlspecialchars($group['name']); ?>
                    <span class="badge bg-secondary ms-1"><?php echo $group['total_count']; ?></span>
                </a>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Detailed Comparison -->
        <div class="comparison-card">
            <h5 class="mb-4"><i class="bi bi-bar-chart-fill me-2"></i>Perbandingan Detail Kelompok</h5>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Kelompok</th>
                            <th>Total</th>
                            <th>SMP</th>
                            <th>SMA</th>
                            <th>Lengkap</th>
                            <th>Pending</th>
                            <th>Mengundurkan Diri</th>
                            <th>Progress Dokumen</th>
                            <th>Progress Wawancara</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($groups_data as $group): ?>
                        <tr>
                            <td>
                                <div class="group-name"><?php echo htmlspecialchars($group['name']); ?></div>
                            </td>
                            <td>
                                <span class="badge bg-primary"><?php echo $group['total_count']; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo $group['smp_count']; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-secondary"><?php echo $group['sma_count']; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-success"><?php echo $group['completed_count']; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-warning"><?php echo $group['pending_count']; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-danger"><?php echo $group['withdrawn_count']; ?></span>
                            </td>
                            <td>
                                <?php 
                                $doc_progress = $group['total_count'] > 0 ? round(($group['avg_documents'] / 11) * 100) : 0;
                                ?>
                                <div class="progress progress-thin">
                                    <div class="progress-bar bg-warning" style="width: <?php echo $doc_progress; ?>%"></div>
                                </div>
                                <small class="text-muted"><?php echo $doc_progress; ?>%</small>
                            </td>
                            <td>
                                <?php 
                                $interview_progress = $group['avg_interviews'] > 0 ? round($group['avg_interviews'] * 25) : 0; // Assuming 4 interviews max
                                ?>
                                <div class="progress progress-thin">
                                    <div class="progress-bar bg-info" style="width: <?php echo min($interview_progress, 100); ?>%"></div>
                                </div>
                                <small class="text-muted"><?php echo min($interview_progress, 100); ?>%</small>
                            </td>
                            <td>
                                <a href="group_view.php?group_id=<?php echo $group['id']; ?>" class="btn btn-primary btn-sm">
                                    <i class="bi bi-eye"></i> Detail
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Performance Insights -->
        <div class="comparison-card">
            <h5 class="mb-4"><i class="bi bi-lightbulb-fill me-2"></i>Insights & Rekomendasi</h5>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>Kelompok dengan Performa Terbaik:</h6>
                    <?php
                    $best_group = null;
                    $best_completion_rate = 0;
                    
                    foreach($groups_data as $group) {
                        if($group['total_count'] > 0) {
                            $completion_rate = ($group['completed_count'] / $group['total_count']) * 100;
                            if($completion_rate > $best_completion_rate) {
                                $best_completion_rate = $completion_rate;
                                $best_group = $group;
                            }
                        }
                    }
                    
                    if($best_group): ?>
                    <div class="alert alert-success">
                        <strong><?php echo htmlspecialchars($best_group['name']); ?></strong><br>
                        Tingkat kelengkapan dokumen: <?php echo round($best_completion_rate); ?>%
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6">
                    <h6>Kelompok yang Perlu Perhatian:</h6>
                    <?php
                    $needs_attention = null;
                    $lowest_completion_rate = 100;
                    
                    foreach($groups_data as $group) {
                        if($group['total_count'] > 0) {
                            $completion_rate = ($group['completed_count'] / $group['total_count']) * 100;
                            if($completion_rate < $lowest_completion_rate) {
                                $lowest_completion_rate = $completion_rate;
                                $needs_attention = $group;
                            }
                        }
                    }
                    
                    if($needs_attention && $lowest_completion_rate < 50): ?>
                    <div class="alert alert-warning">
                        <strong><?php echo htmlspecialchars($needs_attention['name']); ?></strong><br>
                        Tingkat kelengkapan dokumen: <?php echo round($lowest_completion_rate); ?>%
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; <?php echo date("Y"); ?> Seminari Menengah St. Petrus Canisius</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
