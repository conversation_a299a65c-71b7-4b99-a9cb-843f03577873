<?php
// File untuk bottom navigation pada mobile

// Tentukan role pengguna
$userRole = isset($_SESSION['role']) ? $_SESSION['role'] : '';

// Tentukan halaman aktif
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<!-- Bottom Navigation untuk Mobile -->
<div class="mobile-bottom-nav">
    <div class="container">
        <div class="row">
            <?php if ($userRole == 'admin'): ?>
            <!-- Admin Navigation -->
            <div class="col-3 nav-item">
                <a href="admin_dashboard.php" class="nav-link <?php echo ($currentPage == 'admin_dashboard.php') ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-house-door nav-icon"></i>
                    <span class="nav-text">Home</span>
                </a>
            </div>
            <div class="col-3 nav-item">
                <a href="manage_groups.php" class="nav-link <?php echo ($currentPage == 'manage_groups.php') ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-people nav-icon"></i>
                    <span class="nav-text">Kelompok</span>
                </a>
            </div>
            <div class="col-3 nav-item">
                <a href="export_data.php" class="nav-link <?php echo ($currentPage == 'export_data.php') ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-download nav-icon"></i>
                    <span class="nav-text">Ekspor</span>
                </a>
            </div>
            <div class="col-3 nav-item">
                <a href="logout.php" class="nav-link text-secondary">
                    <i class="bi bi-box-arrow-right nav-icon"></i>
                    <span class="nav-text">Logout</span>
                </a>
            </div>
            
            <?php elseif ($userRole == 'group_leader'): ?>
            <!-- Group Leader Navigation -->
            <div class="col-3 nav-item">
                <a href="group_dashboard.php" class="nav-link <?php echo ($currentPage == 'group_dashboard.php') ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-house-door nav-icon"></i>
                    <span class="nav-text">Home</span>
                </a>
            </div>
            <div class="col-3 nav-item">
                <a href="group_dashboard.php#smp-section" class="nav-link <?php echo (strpos($currentPage, 'smp') !== false) ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-person nav-icon"></i>
                    <span class="nav-text">SMP</span>
                </a>
            </div>
            <div class="col-3 nav-item">
                <a href="group_dashboard.php#sma-section" class="nav-link <?php echo (strpos($currentPage, 'sma') !== false) ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-mortarboard nav-icon"></i>
                    <span class="nav-text">SMA</span>
                </a>
            </div>
            <div class="col-3 nav-item">
                <a href="logout.php" class="nav-link text-secondary">
                    <i class="bi bi-box-arrow-right nav-icon"></i>
                    <span class="nav-text">Logout</span>
                </a>
            </div>
            
            <?php else: ?>
            <!-- Public Navigation -->
            <div class="col-4 nav-item">
                <a href="index.php" class="nav-link <?php echo ($currentPage == 'index.php') ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-house-door nav-icon"></i>
                    <span class="nav-text">Home</span>
                </a>
            </div>
            <div class="col-4 nav-item">
                <a href="document_upload.php" class="nav-link <?php echo ($currentPage == 'document_upload.php') ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-file-earmark-arrow-up nav-icon"></i>
                    <span class="nav-text">Upload</span>
                </a>
            </div>
            <div class="col-4 nav-item">
                <a href="login.php" class="nav-link <?php echo ($currentPage == 'login.php') ? 'text-primary' : 'text-secondary'; ?>">
                    <i class="bi bi-person nav-icon"></i>
                    <span class="nav-text">Login</span>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
