<?php
// Include config file
require_once "config.php";

// Check if user is logged in and is admin or group leader
require_login();
if(!is_admin() && !is_group_leader()){
    header("location: login.php");
    exit;
}

// Get group ID from URL parameter
$group_id = isset($_GET['group_id']) ? intval($_GET['group_id']) : 1;

// Get group information
$group_query = "SELECT * FROM groups WHERE id = ?";
$group_stmt = mysqli_prepare($conn, $group_query);
mysqli_stmt_bind_param($group_stmt, "i", $group_id);
mysqli_stmt_execute($group_stmt);
$group_result = mysqli_stmt_get_result($group_stmt);
$group = mysqli_fetch_assoc($group_result);
mysqli_stmt_close($group_stmt);

if (!$group) {
    header("Location: admin_dashboard.php");
    exit;
}

// Get all groups for navigation
$all_groups_query = "SELECT g.id, g.name, 
                     COUNT(a.id) as total_count,
                     COUNT(CASE WHEN a.education_level = 'SMP' THEN 1 END) as smp_count,
                     COUNT(CASE WHEN a.education_level = 'SMA' THEN 1 END) as sma_count
                     FROM groups g 
                     LEFT JOIN applicants a ON g.id = a.group_id 
                     GROUP BY g.id, g.name 
                     ORDER BY g.id";
$all_groups_result = mysqli_query($conn, $all_groups_query);
$all_groups = [];
while ($row = mysqli_fetch_assoc($all_groups_result)) {
    $all_groups[] = $row;
}

// Get applicants for this group
$applicants_query = "SELECT a.*,
                     (SELECT COUNT(*) FROM documents WHERE applicant_id = a.id) AS doc_count,
                     (SELECT COUNT(*) FROM interviews WHERE applicant_id = a.id AND status = 'completed') AS completed_interviews,
                     (SELECT COUNT(*) FROM interviews WHERE applicant_id = a.id) AS total_interviews
                     FROM applicants a
                     WHERE a.group_id = ?
                     ORDER BY a.education_level, a.registration_number";

$applicants_stmt = mysqli_prepare($conn, $applicants_query);
mysqli_stmt_bind_param($applicants_stmt, "i", $group_id);
mysqli_stmt_execute($applicants_stmt);
$applicants_result = mysqli_stmt_get_result($applicants_stmt);

$smp_applicants = [];
$sma_applicants = [];

while ($row = mysqli_fetch_assoc($applicants_result)) {
    if ($row['education_level'] == 'SMP') {
        $smp_applicants[] = $row;
    } else {
        $sma_applicants[] = $row;
    }
}

mysqli_stmt_close($applicants_stmt);

// Calculate statistics
$total_applicants = count($smp_applicants) + count($sma_applicants);
$smp_count = count($smp_applicants);
$sma_count = count($sma_applicants);

// Function to get missing documents for an applicant
function get_missing_documents($applicant_id) {
    global $conn;
    
    $doc_types = array(
        'parent_permission_letter' => 'Surat Ijin Ortu',
        'birth_certificate' => 'Akta Kelahiran',
        'baptism_certificate' => 'Surat Baptis',
        'confirmation_certificate' => 'Surat Krisma',
        'family_card' => 'Kartu Keluarga',
        'parish_letter' => 'Rekom Paroki',
        'school_letter' => 'Rekom Sekolah',
        'parents_marriage_certificate' => 'Akta Pernikahan Ortu',
        'diploma' => 'Ijasah',
        'report_card' => 'Rapor',
        'questionnaire' => 'Kuesioner'
    );
    
    $missing_docs = array_keys($doc_types);
    
    $doc_sql = "SELECT document_type FROM documents WHERE applicant_id = ?";
    if($doc_stmt = mysqli_prepare($conn, $doc_sql)) {
        mysqli_stmt_bind_param($doc_stmt, "i", $applicant_id);
        
        if(mysqli_stmt_execute($doc_stmt)) {
            $doc_result = mysqli_stmt_get_result($doc_stmt);
            
            while($doc = mysqli_fetch_assoc($doc_result)) {
                $index = array_search($doc['document_type'], $missing_docs);
                if($index !== false) {
                    unset($missing_docs[$index]);
                }
            }
        }
        
        mysqli_stmt_close($doc_stmt);
    }
    
    // Convert document types to readable names
    $missing_doc_names = [];
    foreach($missing_docs as $doc_type) {
        $missing_doc_names[] = $doc_types[$doc_type];
    }
    
    return $missing_doc_names;
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($group['name']); ?> - Seminari Menengah St. Petrus Canisius</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            padding: 15px 0;
            margin-bottom: 30px;
        }
        .logo img {
            width: 40px;
            height: auto;
            margin-right: 10px;
        }
        .stats-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            transition: all 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stats-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #0d6efd;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            color: #6c757d;
        }
        .dashboard-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .group-nav {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        .group-nav-item {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            color: #6c757d;
            background-color: #f8f9fa;
            transition: all 0.3s;
        }
        .group-nav-item:hover {
            color: #0d6efd;
            background-color: #e3f2fd;
            text-decoration: none;
        }
        .group-nav-item.active {
            color: white;
            background-color: #0d6efd;
        }
        .profile-pic {
            width: 45px;
            height: 45px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .profile-pic:hover {
            transform: scale(1.05);
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 3px 8px;
        }
        .doc-badge {
            background-color: #ffc107;
            color: #000;
        }
        .interview-badge {
            background-color: #6f42c1;
            color: #fff;
            margin-left: 5px;
        }
        .wave-badge {
            background-color: #0dcaf0;
            color: #000;
            font-size: 0.75rem;
            padding: 2px 5px;
            border-radius: 3px;
            margin-left: 3px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center logo">
                    <img src="assets/img/logo.png" alt="Logo Seminari">
                    <h5 class="mb-0"><?php echo htmlspecialchars($group['name']); ?></h5>
                    <p class="mb-0 ms-3 text-muted">Seminari Menengah St. Petrus Canisius</p>
                </div>
                <div>
                    <?php if(is_admin()): ?>
                    <a href="admin_dashboard.php" class="btn btn-outline-primary btn-sm">← Kembali ke Dashboard Admin</a>
                    <?php else: ?>
                    <a href="group_dashboard.php" class="btn btn-outline-primary btn-sm">← Kembali ke Dashboard Kelompok</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Group Navigation -->
        <div class="group-nav">
            <h6 class="mb-3"><i class="bi bi-people-fill me-2"></i>Navigasi Kelompok</h6>
            <?php foreach($all_groups as $nav_group): ?>
            <a href="group_view.php?group_id=<?php echo $nav_group['id']; ?>" 
               class="group-nav-item <?php echo ($nav_group['id'] == $group_id) ? 'active' : ''; ?>">
                <?php echo htmlspecialchars($nav_group['name']); ?>
                <span class="badge bg-secondary ms-1"><?php echo $nav_group['total_count']; ?></span>
            </a>
            <?php endforeach; ?>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <i class="bi bi-people-fill stats-icon"></i>
                    <div class="stats-number"><?php echo $total_applicants; ?></div>
                    <div class="stats-label">Total Pendaftar</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <i class="bi bi-person-fill stats-icon" style="color: #0d6efd;"></i>
                    <div class="stats-number"><?php echo $smp_count; ?></div>
                    <div class="stats-label">Pendaftar SMP</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <i class="bi bi-mortarboard-fill stats-icon" style="color: #6f42c1;"></i>
                    <div class="stats-number"><?php echo $sma_count; ?></div>
                    <div class="stats-label">Pendaftar SMA</div>
                </div>
            </div>
        </div>

        <!-- SMP Applicants -->
        <?php if ($smp_count > 0): ?>
        <div class="dashboard-card">
            <h5 class="mb-3"><i class="bi bi-person-fill me-2"></i>Pendaftar SMP</h5>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>NO. PENDAFTARAN</th>
                            <th>NAMA</th>
                            <th>ASAL SEKOLAH</th>
                            <th>PAROKI</th>
                            <th>DOKUMEN</th>
                            <th>WAWANCARA</th>
                            <th>STATUS</th>
                            <th>AKSI</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($smp_applicants as $row): ?>
                        <tr>
                            <td>
                                <?php
                                $reg_number = htmlspecialchars($row['registration_number']);
                                $wave = get_wave_from_registration_number($reg_number);
                                echo $reg_number;
                                echo '<span class="wave-badge">Gel. ' . $wave . '</span>';
                                ?>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if(!empty($row['photo_path'])): ?>
                                    <div class="me-2">
                                        <img src="<?php echo htmlspecialchars($row['photo_path']); ?>" class="profile-pic">
                                    </div>
                                    <?php else: ?>
                                    <div class="profile-pic me-2 bg-secondary d-flex align-items-center justify-content-center">
                                        <i class="bi bi-person-fill text-white"></i>
                                    </div>
                                    <?php endif; ?>
                                    <div>
                                        <p class="mb-0 fw-bold"><?php echo htmlspecialchars($row['name']); ?></p>
                                        <small class="text-muted"><?php echo htmlspecialchars($row['birth_place'] . ', ' . date('d/m/Y', strtotime($row['birth_date']))); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($row['school_origin']); ?></td>
                            <td><?php echo htmlspecialchars($row['parish_origin']); ?></td>
                            <td>
                                <span class="badge doc-badge">
                                    <i class="bi bi-files me-1"></i>
                                    <?php echo $row['doc_count']; ?>/11
                                </span>
                            </td>
                            <td>
                                <span class="badge interview-badge">
                                    <i class="bi bi-chat-dots me-1"></i>
                                    <?php echo $row['completed_interviews']; ?>/<?php echo $row['total_interviews']; ?>
                                </span>
                            </td>
                            <td>
                                <?php if($row['status'] == 'pending'):
                                    $missing_docs = get_missing_documents($row['id']);
                                    $missing_docs_text = implode(", ", $missing_docs);
                                ?>
                                <span class="badge bg-warning status-badge" data-bs-toggle="tooltip" data-bs-html="true" title="Dokumen yang belum diupload:<br><?php echo $missing_docs_text; ?>">Belum Lengkap <i class="bi bi-info-circle-fill"></i></span>
                                <?php elseif($row['status'] == 'completed'): ?>
                                <span class="badge bg-success status-badge">Lengkap</span>
                                <?php else: ?>
                                <span class="badge bg-danger status-badge">Mengundurkan diri</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="applicant_detail.php?id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="edit_data.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>

        <!-- SMA Applicants -->
        <?php if ($sma_count > 0): ?>
        <div class="dashboard-card">
            <h5 class="mb-3"><i class="bi bi-mortarboard-fill me-2"></i>Pendaftar SMA</h5>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>NO. PENDAFTARAN</th>
                            <th>NAMA</th>
                            <th>ASAL SEKOLAH</th>
                            <th>PAROKI</th>
                            <th>DOKUMEN</th>
                            <th>WAWANCARA</th>
                            <th>STATUS</th>
                            <th>AKSI</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($sma_applicants as $row): ?>
                        <tr>
                            <td>
                                <?php
                                $reg_number = htmlspecialchars($row['registration_number']);
                                $wave = get_wave_from_registration_number($reg_number);
                                echo $reg_number;
                                echo '<span class="wave-badge">Gel. ' . $wave . '</span>';
                                ?>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if(!empty($row['photo_path'])): ?>
                                    <div class="me-2">
                                        <img src="<?php echo htmlspecialchars($row['photo_path']); ?>" class="profile-pic">
                                    </div>
                                    <?php else: ?>
                                    <div class="profile-pic me-2 bg-secondary d-flex align-items-center justify-content-center">
                                        <i class="bi bi-person-fill text-white"></i>
                                    </div>
                                    <?php endif; ?>
                                    <div>
                                        <p class="mb-0 fw-bold"><?php echo htmlspecialchars($row['name']); ?></p>
                                        <small class="text-muted"><?php echo htmlspecialchars($row['birth_place'] . ', ' . date('d/m/Y', strtotime($row['birth_date']))); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($row['school_origin']); ?></td>
                            <td><?php echo htmlspecialchars($row['parish_origin']); ?></td>
                            <td>
                                <span class="badge doc-badge">
                                    <i class="bi bi-files me-1"></i>
                                    <?php echo $row['doc_count']; ?>/11
                                </span>
                            </td>
                            <td>
                                <span class="badge interview-badge">
                                    <i class="bi bi-chat-dots me-1"></i>
                                    <?php echo $row['completed_interviews']; ?>/<?php echo $row['total_interviews']; ?>
                                </span>
                            </td>
                            <td>
                                <?php if($row['status'] == 'pending'):
                                    $missing_docs = get_missing_documents($row['id']);
                                    $missing_docs_text = implode(", ", $missing_docs);
                                ?>
                                <span class="badge bg-warning status-badge" data-bs-toggle="tooltip" data-bs-html="true" title="Dokumen yang belum diupload:<br><?php echo $missing_docs_text; ?>">Belum Lengkap <i class="bi bi-info-circle-fill"></i></span>
                                <?php elseif($row['status'] == 'completed'): ?>
                                <span class="badge bg-success status-badge">Lengkap</span>
                                <?php else: ?>
                                <span class="badge bg-danger status-badge">Mengundurkan diri</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="applicant_detail.php?id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="edit_data.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>

        <!-- Empty State -->
        <?php if ($total_applicants == 0): ?>
        <div class="dashboard-card text-center py-5">
            <i class="bi bi-people" style="font-size: 4rem; color: #6c757d;"></i>
            <h4 class="mt-3 text-muted">Belum Ada Pendaftar</h4>
            <p class="text-muted">Kelompok ini belum memiliki pendaftar.</p>
        </div>
        <?php endif; ?>
    </div>

    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; <?php echo date("Y"); ?> Seminari Menengah St. Petrus Canisius</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    placement: 'top',
                    trigger: 'hover focus',
                    html: true
                })
            })
        });
    </script>
</body>
</html>
