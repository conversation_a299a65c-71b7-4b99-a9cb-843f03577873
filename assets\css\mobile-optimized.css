/* CSS untuk optimasi mobile */

/* Penyesuaian untuk layar kecil */
@media (max-width: 576px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .dashboard-card, .upload-card, .form-container {
        padding: 15px 10px;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        margin-bottom: 0;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .header h5 {
        font-size: 1rem;
    }
    
    .header .logo img {
        width: 30px;
    }
    
    /* Sembunyikan beberapa kolom tabel pada mobile */
    .mobile-hide {
        display: none;
    }
    
    /* Buat tabel lebih mudah dibaca di mobile */
    .table-mobile-friendly th,
    .table-mobile-friendly td {
        padding: 0.5rem 0.25rem;
        font-size: 0.85rem;
    }
    
    /* Perkecil pagination */
    .pagination .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    /* Penyesuaian untuk card */
    .card-body {
        padding: 0.75rem;
    }
    
    /* Penyesuaian untuk form */
    .form-label {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }
    
    .form-control, .form-select {
        padding: 0.375rem 0.5rem;
        font-size: 0.9rem;
    }
    
    /* Penyesuaian untuk alert */
    .alert {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
    
    /* Penyesuaian untuk modal */
    .modal-body {
        padding: 0.75rem;
    }
    
    .modal-footer {
        padding: 0.5rem;
    }
    
    /* Penyesuaian untuk badge */
    .badge {
        font-size: 0.7rem;
    }
    
    /* Penyesuaian untuk dropdown */
    .dropdown-menu {
        font-size: 0.9rem;
    }
    
    .dropdown-item {
        padding: 0.25rem 0.75rem;
    }
}

/* Perbaikan untuk tampilan form di mobile */
@media (max-width: 768px) {
    .form-container {
        padding: 15px;
    }
    
    .form-label {
        margin-bottom: 0.25rem;
    }
    
    .form-control, .form-select {
        padding: 0.375rem 0.5rem;
        font-size: 0.9rem;
    }
    
    /* Buat tombol lebih besar untuk touch */
    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
    
    /* Penyesuaian untuk card */
    .card {
        margin-bottom: 15px;
    }
    
    /* Penyesuaian untuk tab */
    .nav-tabs .nav-link, .nav-pills .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
}

/* Bottom Navigation untuk Mobile */
.mobile-bottom-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 8px 0;
}

@media (max-width: 768px) {
    .mobile-bottom-nav {
        display: block;
    }
    
    body {
        padding-bottom: 60px;
    }
    
    .mobile-bottom-nav .nav-item {
        text-align: center;
    }
    
    .mobile-bottom-nav .nav-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0;
    }
    
    .mobile-bottom-nav .nav-icon {
        font-size: 1.5rem;
        margin-bottom: 2px;
    }
    
    .mobile-bottom-nav .nav-text {
        font-size: 0.7rem;
    }
}

/* Optimasi untuk tabel */
.table-mobile-friendly {
    width: 100%;
}

@media (max-width: 768px) {
    .table-mobile-friendly thead {
        display: none;
    }
    
    .table-mobile-friendly, 
    .table-mobile-friendly tbody, 
    .table-mobile-friendly tr, 
    .table-mobile-friendly td {
        display: block;
        width: 100%;
    }
    
    .table-mobile-friendly tr {
        margin-bottom: 15px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }
    
    .table-mobile-friendly td {
        position: relative;
        padding-left: 50%;
        text-align: right;
        border-bottom: 1px solid #dee2e6;
    }
    
    .table-mobile-friendly td:before {
        content: attr(data-label);
        position: absolute;
        left: 0.75rem;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: bold;
    }
    
    .table-mobile-friendly td:last-child {
        border-bottom: 0;
    }
    
    .table-mobile-friendly .btn-group {
        display: flex;
        justify-content: flex-end;
    }
}
