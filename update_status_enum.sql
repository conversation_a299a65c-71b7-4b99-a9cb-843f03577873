-- Script untuk mengubah status 'rejected' menjadi 'mengundurkan_diri'

-- 1. Ubah tipe enum pada kolom status di tabel applicants
ALTER TABLE applicants 
MODIFY COLUMN status ENUM('pending', 'completed', 'mengundurkan_diri') DEFAULT 'pending';

-- 2. Update data yang sudah ada, mengubah 'rejected' menjadi 'mengundurkan_diri'
UPDATE applicants 
SET status = 'mengundurkan_diri' 
WHERE status = 'rejected';

-- 3. Tampilkan data yang sudah diupdate untuk verifikasi
SELECT id, name, status FROM applicants WHERE status = 'mengundurkan_diri';
