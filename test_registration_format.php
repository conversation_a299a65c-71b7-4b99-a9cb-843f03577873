<?php
// Test script untuk memverifikasi format nomor pendaftaran P26/A26
require_once "config.php";

echo "<h2>🧪 Test Format Nomor Pendaftaran P26/A26</h2>";

// Test 1: Test fungsi generate_registration_number
echo "<h3>1. Test Fungsi Generate Registration Number</h3>";

// Test untuk SMP
$smp_number = generate_registration_number('SMP', 2);
echo "SMP Gelombang 2: <strong>$smp_number</strong><br>";

// Test untuk SMA
$sma_number = generate_registration_number('SMA', 2);
echo "SMA Gelombang 2: <strong>$sma_number</strong><br>";

// Validasi format
if (preg_match('/^P26\.2\.\d{3}$/', $smp_number)) {
    echo "✅ Format SMP benar (P26.2.xxx)<br>";
} else {
    echo "❌ Format SMP salah: $smp_number<br>";
}

if (preg_match('/^A26\.2\.\d{3}$/', $sma_number)) {
    echo "✅ Format SMA benar (A26.2.xxx)<br>";
} else {
    echo "❌ Format SMA salah: $sma_number<br>";
}

// Test 2: Cek distribusi nomor pendaftaran saat ini
echo "<h3>2. Distribusi Nomor Pendaftaran Saat Ini</h3>";

$formats = [
    'P25' => "SELECT COUNT(*) as count FROM applicants WHERE registration_number LIKE 'P25.%'",
    'A25' => "SELECT COUNT(*) as count FROM applicants WHERE registration_number LIKE 'A25.%'",
    'P26' => "SELECT COUNT(*) as count FROM applicants WHERE registration_number LIKE 'P26.%'",
    'A26' => "SELECT COUNT(*) as count FROM applicants WHERE registration_number LIKE 'A26.%'"
];

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Format</th><th>Jumlah</th><th>Status</th></tr>";

foreach ($formats as $format => $query) {
    $result = mysqli_query($conn, $query);
    $count = 0;
    if ($row = mysqli_fetch_assoc($result)) {
        $count = $row['count'];
    }
    
    $status = "";
    if (in_array($format, ['P25', 'A25'])) {
        $status = $count > 0 ? "⚠️ Perlu diupdate" : "✅ Sudah diupdate";
    } else {
        $status = $count > 0 ? "✅ Format baru" : "ℹ️ Belum ada data";
    }
    
    echo "<tr>";
    echo "<td>$format.x.xxx</td>";
    echo "<td>$count</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";

// Test 3: Sample data dengan format lama dan baru
echo "<h3>3. Sample Data Nomor Pendaftaran</h3>";

$sample_query = "SELECT registration_number, name, education_level 
                 FROM applicants 
                 ORDER BY registration_number 
                 LIMIT 10";
$sample_result = mysqli_query($conn, $sample_query);

if ($sample_result && mysqli_num_rows($sample_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Nomor Pendaftaran</th><th>Nama</th><th>Jenjang</th><th>Format</th></tr>";
    
    while ($row = mysqli_fetch_assoc($sample_result)) {
        $reg_number = $row['registration_number'];
        $format_status = "";
        
        if (preg_match('/^[PA]25\./', $reg_number)) {
            $format_status = "❌ Format Lama";
        } elseif (preg_match('/^[PA]26\./', $reg_number)) {
            $format_status = "✅ Format Baru";
        } else {
            $format_status = "❓ Format Tidak Dikenal";
        }
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($reg_number) . "</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>" . $row['education_level'] . "</td>";
        echo "<td>$format_status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Tidak ada data pendaftar.<br>";
}

// Test 4: Test regex validation
echo "<h3>4. Test Regex Validation</h3>";

$test_numbers = [
    'P26.2.001' => true,
    'A26.2.001' => true,
    'P25.2.001' => true, // Valid format tapi tahun lama
    'A25.2.001' => true, // Valid format tapi tahun lama
    'P26.2.1' => false,   // Urutan tidak 3 digit
    'P26.2.1234' => false, // Urutan lebih dari 3 digit
    'B26.2.001' => false,  // Prefix salah
    'P26.5.001' => true,   // Gelombang 5 (valid)
    'P26.001' => false,    // Format salah
];

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Nomor Test</th><th>Expected</th><th>Result</th><th>Status</th></tr>";

foreach ($test_numbers as $number => $expected) {
    $result = preg_match('/^[A-Z]\d{2}\.\d\.\d{3}$/', $number);
    $status = ($result == $expected) ? "✅ Pass" : "❌ Fail";
    
    echo "<tr>";
    echo "<td>$number</td>";
    echo "<td>" . ($expected ? "Valid" : "Invalid") . "</td>";
    echo "<td>" . ($result ? "Valid" : "Invalid") . "</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";

// Test 5: Test sequence generation
echo "<h3>5. Test Sequence Generation</h3>";

// Simulate multiple registrations
echo "Simulasi generate 5 nomor SMP berturut-turut:<br>";
for ($i = 1; $i <= 5; $i++) {
    $test_number = generate_registration_number('SMP', 2);
    echo "$i. $test_number<br>";
}

echo "<br>Simulasi generate 3 nomor SMA berturut-turut:<br>";
for ($i = 1; $i <= 3; $i++) {
    $test_number = generate_registration_number('SMA', 2);
    echo "$i. $test_number<br>";
}

// Test 6: Check for duplicates
echo "<h3>6. Check for Duplicate Registration Numbers</h3>";

$duplicate_query = "SELECT registration_number, COUNT(*) as count 
                    FROM applicants 
                    GROUP BY registration_number 
                    HAVING COUNT(*) > 1";
$duplicate_result = mysqli_query($conn, $duplicate_query);

if ($duplicate_result && mysqli_num_rows($duplicate_result) > 0) {
    echo "❌ Ditemukan nomor pendaftaran duplikat:<br>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Nomor Pendaftaran</th><th>Jumlah Duplikat</th></tr>";
    
    while ($row = mysqli_fetch_assoc($duplicate_result)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['registration_number']) . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "✅ Tidak ada nomor pendaftaran duplikat<br>";
}

echo "<h3>🎯 Summary</h3>";
echo "<p>Jika semua test menunjukkan ✅, maka format nomor pendaftaran P26/A26 sudah berfungsi dengan benar!</p>";

echo "<h3>🔗 Action Links</h3>";
echo "<ul>";
echo "<li><a href='update_to_26_format.php'>Update Format ke P26/A26</a> - Ubah semua nomor dari P25/A25 ke P26/A26</li>";
echo "<li><a href='index.php'>Test Pendaftaran Baru</a> - Test generate nomor untuk pendaftar baru</li>";
echo "<li><a href='admin_dashboard.php'>Admin Dashboard</a> - Lihat semua data pendaftar</li>";
echo "</ul>";

echo "<h3>📋 Manual Testing Checklist</h3>";
echo "<ol>";
echo "<li>Daftar pendaftar baru dan pastikan dapat nomor P26/A26</li>";
echo "<li>Cek apakah nomor berurutan dengan benar</li>";
echo "<li>Test edit nomor pendaftaran dengan format baru</li>";
echo "<li>Pastikan tidak ada duplikat nomor</li>";
echo "<li>Verifikasi foto tersimpan dengan nama file yang benar</li>";
echo "</ol>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}
h2, h3 {
    color: #333;
}
table {
    background-color: white;
    padding: 10px;
}
th {
    background-color: #0d6efd;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
ul, ol {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
