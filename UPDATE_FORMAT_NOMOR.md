# 📋 **Update Format Nomor Pendaftaran ke P26/A26**

## 🎯 **Deskripsi Perubahan**

Format nomor pendaftaran telah diubah dari tahun 2025 (P25/A25) menjadi tahun 2026 (P26/A26) untuk tahun pelajaran baru.

### **Format Lama vs Format Baru:**
- **Format Lama**: P25.2.001, A25.2.001
- **Format Baru**: P26.2.001, A26.2.001

## 🔧 **Perubahan yang Dilakukan**

### **1. Fungsi Generate Registration Number (`config.php`)**
```php
// Sebelum:
$year = date('y'); // Menggunakan tahun saat ini (25)

// Sesudah:
$year = "26"; // Fixed ke 26 untuk tahun pelajaran 2026
```

### **2. Update Dokumentasi dan Contoh**
- `update_registration_numbers.php` - Contoh format diubah ke P26.2.001
- `edit_registration_number.php` - Placeholder dan validasi diubah ke P26.2.001
- <PERSON><PERSON><PERSON> pesan error dan help text diupdate

### **3. <PERSON><PERSON><PERSON> Migration (`update_to_26_format.php`)**
- Script untuk mengubah semua nomor existing dari P25/A25 ke P26/A26
- Preview perubahan sebelum eksekusi
- Logging perubahan untuk audit trail
- Statistik before/after

### **4. Script Testing (`test_registration_format.php`)**
- Test fungsi generate registration number
- Validasi format regex
- Check duplikasi nomor
- Simulasi sequence generation

## 📁 **File yang Dimodifikasi/Dibuat**

### **File yang Dimodifikasi:**
1. **`config.php`** - Fungsi `generate_registration_number()`
2. **`update_registration_numbers.php`** - Contoh format dan dokumentasi
3. **`edit_registration_number.php`** - Validasi dan placeholder

### **File Baru:**
1. **`update_to_26_format.php`** - Script migration P25→P26
2. **`test_registration_format.php`** - Script testing format baru
3. **`UPDATE_FORMAT_NOMOR.md`** - Dokumentasi perubahan

## 🚀 **Langkah-langkah Implementasi**

### **1. Update Fungsi Generate (✅ Selesai)**
```bash
# Fungsi di config.php sudah diubah
# Pendaftar baru akan otomatis dapat nomor P26/A26
```

### **2. Migrate Data Existing**
```bash
# Jalankan script migration
http://your-domain/update_to_26_format.php

# Langkah:
1. Klik "Preview Perubahan" untuk melihat data yang akan diubah
2. Klik "Update Semua" untuk eksekusi perubahan
3. Verifikasi hasil dengan melihat statistik
```

### **3. Testing dan Verifikasi**
```bash
# Jalankan script testing
http://your-domain/test_registration_format.php

# Verifikasi:
1. Fungsi generate menghasilkan format P26/A26
2. Tidak ada duplikasi nomor
3. Sequence generation berjalan benar
4. Regex validation berfungsi
```

## 📊 **Format Nomor Pendaftaran**

### **Struktur Format:**
```
P26.2.001
│││ │ │││
│││ │ └── Urutan pendaftaran (001-999)
│││ └──── Gelombang pendaftaran (1-9)
││└────── Tahun pelajaran (26 = 2026)
│└─────── Jenjang (P = SMP, A = SMA)
└──────── Prefix
```

### **Contoh Nomor:**
- **SMP Gelombang 1**: P26.1.001, P26.1.002, P26.1.003...
- **SMP Gelombang 2**: P26.2.001, P26.2.002, P26.2.003...
- **SMA Gelombang 1**: A26.1.001, A26.1.002, A26.1.003...
- **SMA Gelombang 2**: A26.2.001, A26.2.002, A26.2.003...

## 🔍 **Validasi dan Rules**

### **Regex Pattern:**
```regex
^[A-Z]\d{2}\.\d\.\d{3}$
```

### **Validation Rules:**
1. **Prefix**: Harus P (SMP) atau A (SMA)
2. **Tahun**: 2 digit (26 untuk tahun 2026)
3. **Gelombang**: 1 digit (1-9)
4. **Urutan**: 3 digit dengan leading zero (001-999)
5. **Separator**: Titik (.) antar komponen

### **Contoh Valid:**
- ✅ P26.2.001
- ✅ A26.1.150
- ✅ P26.3.999

### **Contoh Invalid:**
- ❌ P26.2.1 (urutan kurang dari 3 digit)
- ❌ B26.2.001 (prefix salah)
- ❌ P26.10.001 (gelombang lebih dari 1 digit)
- ❌ P26.2.1000 (urutan lebih dari 3 digit)

## 🛠️ **Troubleshooting**

### **Masalah Umum:**

#### **1. Nomor Masih Format P25/A25**
```bash
# Solusi:
1. Pastikan config.php sudah diupdate
2. Jalankan update_to_26_format.php untuk data existing
3. Clear browser cache
```

#### **2. Duplikasi Nomor**
```bash
# Solusi:
1. Jalankan test_registration_format.php
2. Check section "Check for Duplicate Registration Numbers"
3. Manual fix jika ditemukan duplikat
```

#### **3. Sequence Tidak Berurutan**
```bash
# Solusi:
1. Check query di generate_registration_number()
2. Pastikan MAX() function bekerja dengan benar
3. Verifikasi data di database
```

## 📈 **Monitoring dan Maintenance**

### **Regular Checks:**
1. **Weekly**: Jalankan `test_registration_format.php` untuk check duplikasi
2. **Monthly**: Verifikasi sequence generation masih benar
3. **Per Gelombang**: Update gelombang di sistem jika diperlukan

### **Backup Recommendations:**
1. Backup database sebelum migration
2. Export registration_number_logs untuk audit trail
3. Keep old format documentation untuk reference

## 🎯 **Next Steps**

### **Immediate Actions:**
1. ✅ Update fungsi generate
2. ⏳ Run migration script untuk data existing
3. ⏳ Test dengan pendaftar baru
4. ⏳ Update user documentation

### **Future Considerations:**
1. **Auto Year Update**: Pertimbangkan auto-update tahun berdasarkan tanggal
2. **Gelombang Management**: Sistem untuk manage gelombang pendaftaran
3. **Bulk Operations**: Tools untuk bulk update nomor jika diperlukan
4. **API Integration**: Jika ada integrasi dengan sistem lain

## 📞 **Support**

Jika ada masalah dengan format nomor pendaftaran:
1. Jalankan `test_registration_format.php` untuk diagnosis
2. Check `registration_number_logs` untuk history perubahan
3. Backup database sebelum melakukan perubahan manual
4. Dokumentasikan setiap perubahan yang dilakukan

---

**Last Updated**: 2025-01-12  
**Version**: 1.0  
**Status**: Ready for Implementation
