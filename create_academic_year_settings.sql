-- <PERSON><PERSON>t untuk membuat tabel pengaturan tahun akademik

-- Membuat tabel academic_year_settings untuk mengelola tahun akademik
CREATE TABLE IF NOT EXISTS academic_year_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_name VARCHAR(50) NOT NULL UNIQUE,
    setting_value VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default settings
INSERT INTO academic_year_settings (setting_name, setting_value, description) VALUES
('current_academic_year', '26', 'Tahun akademik saat ini (format 2 digit: 26 untuk 2026)'),
('academic_year_mode', 'manual', 'Mode pengaturan tahun: manual atau auto'),
('auto_year_start_month', '7', 'Bulan mulai tahun akademik baru (1-12, default: Juli = 7)'),
('auto_year_start_day', '1', 'Tanggal mulai tahun akademik baru (1-31, default: 1)'),
('current_wave', '2', 'Gelombang pendaftaran saat ini (1-9)'),
('registration_active', 'true', 'Status pendaftaran aktif (true/false)'),
('year_transition_notification', 'true', 'Tampilkan notifikasi saat transisi tahun (true/false)')
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
description = VALUES(description);

-- Membuat tabel academic_year_history untuk mencatat perubahan tahun akademik
CREATE TABLE IF NOT EXISTS academic_year_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    old_year VARCHAR(10),
    new_year VARCHAR(10),
    transition_type ENUM('manual', 'automatic') DEFAULT 'manual',
    transition_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by INT,
    notes TEXT,
    affected_registrations INT DEFAULT 0,
    FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Index untuk performa
CREATE INDEX idx_academic_year_settings_name ON academic_year_settings(setting_name);
CREATE INDEX idx_academic_year_settings_active ON academic_year_settings(is_active);
CREATE INDEX idx_academic_year_history_date ON academic_year_history(transition_date);

-- View untuk kemudahan akses pengaturan aktif
CREATE OR REPLACE VIEW current_academic_settings AS
SELECT 
    setting_name,
    setting_value,
    description,
    updated_at
FROM academic_year_settings 
WHERE is_active = TRUE;

-- Trigger untuk update timestamp
DELIMITER $$
CREATE TRIGGER update_academic_year_settings_timestamp 
    BEFORE UPDATE ON academic_year_settings
    FOR EACH ROW 
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- Function untuk mendapatkan tahun akademik saat ini
DELIMITER $$
CREATE FUNCTION get_current_academic_year() 
RETURNS VARCHAR(10)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE current_year VARCHAR(10);
    
    SELECT setting_value INTO current_year 
    FROM academic_year_settings 
    WHERE setting_name = 'current_academic_year' AND is_active = TRUE
    LIMIT 1;
    
    -- Fallback jika tidak ada setting
    IF current_year IS NULL THEN
        SET current_year = '26';
    END IF;
    
    RETURN current_year;
END$$
DELIMITER ;

-- Function untuk mendapatkan gelombang saat ini
DELIMITER $$
CREATE FUNCTION get_current_wave() 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE current_wave INT;
    
    SELECT CAST(setting_value AS UNSIGNED) INTO current_wave 
    FROM academic_year_settings 
    WHERE setting_name = 'current_wave' AND is_active = TRUE
    LIMIT 1;
    
    -- Fallback jika tidak ada setting
    IF current_wave IS NULL THEN
        SET current_wave = 2;
    END IF;
    
    RETURN current_wave;
END$$
DELIMITER ;

-- Procedure untuk update tahun akademik
DELIMITER $$
CREATE PROCEDURE update_academic_year(
    IN new_year VARCHAR(10),
    IN transition_type ENUM('manual', 'automatic'),
    IN user_id INT,
    IN notes TEXT
)
BEGIN
    DECLARE old_year VARCHAR(10);
    DECLARE affected_count INT DEFAULT 0;
    
    -- Mulai transaksi
    START TRANSACTION;
    
    -- Dapatkan tahun lama
    SELECT setting_value INTO old_year 
    FROM academic_year_settings 
    WHERE setting_name = 'current_academic_year' AND is_active = TRUE;
    
    -- Update tahun akademik
    UPDATE academic_year_settings 
    SET setting_value = new_year, updated_by = user_id
    WHERE setting_name = 'current_academic_year';
    
    -- Hitung jumlah registrasi yang terpengaruh (opsional untuk statistik)
    SELECT COUNT(*) INTO affected_count 
    FROM applicants 
    WHERE registration_number LIKE CONCAT('%', old_year, '.%');
    
    -- Catat perubahan ke history
    INSERT INTO academic_year_history (old_year, new_year, transition_type, changed_by, notes, affected_registrations)
    VALUES (old_year, new_year, transition_type, user_id, notes, affected_count);
    
    COMMIT;
END$$
DELIMITER ;

-- Tampilkan hasil
SELECT 'Academic Year Settings table created successfully' as status;
SELECT * FROM academic_year_settings;
