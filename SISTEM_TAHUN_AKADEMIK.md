# 📅 **Sistem Manajemen Tahun Akademik**

## 🎯 **Overview**

Sistem ini menyediakan manajemen tahun akademik yang fleksibel dengan dua mode operasi:
- **Manual Mode**: Admin mengatur tahun akademik secara manual
- **Auto Mode**: Sistem menghitung tahun akademik berdasarkan tanggal

## 🏗️ **Arsitektur Sistem**

### **1. Database Tables**

#### **`academic_year_settings`**
```sql
- id (Primary Key)
- setting_name (VARCHAR) - <PERSON>a pengaturan
- setting_value (VARCHAR) - <PERSON><PERSON> pengaturan  
- description (TEXT) - Deskripsi pengaturan
- is_active (BOOLEAN) - Status aktif
- created_at, updated_at (TIMESTAMP)
- updated_by (INT) - User yang mengupdate
```

#### **`academic_year_history`**
```sql
- id (Primary Key)
- old_year (VARCHAR) - <PERSON><PERSON> lama
- new_year (VARCHAR) - <PERSON>hun baru
- transition_type (ENUM) - manual/automatic
- transition_date (TIMESTAMP)
- changed_by (INT) - User yang melakukan perubahan
- notes (TEXT) - Catatan
- affected_registrations (INT) - Jumlah data terpengaruh
```

### **2. Functions & Procedures**

#### **PHP Functions:**
- `get_current_academic_year()` - Mendapatkan tahun akademik saat ini
- `calculate_auto_academic_year()` - Menghitung tahun otomatis
- `get_current_wave()` - Mendapatkan gelombang saat ini
- `generate_registration_number()` - Generate nomor pendaftaran

#### **MySQL Functions:**
- `get_current_academic_year()` - Function database untuk tahun saat ini
- `get_current_wave()` - Function database untuk gelombang saat ini

#### **MySQL Procedures:**
- `update_academic_year()` - Procedure untuk update tahun akademik

## ⚙️ **Pengaturan Sistem**

### **Default Settings:**
```
current_academic_year: "26"
academic_year_mode: "manual"
auto_year_start_month: "7" (Juli)
auto_year_start_day: "1"
current_wave: "2"
registration_active: "true"
year_transition_notification: "true"
```

### **Mode Manual:**
- Admin mengatur tahun akademik secara eksplisit
- Perubahan dilakukan melalui interface admin
- Tahun tidak berubah otomatis

### **Mode Otomatis:**
- Sistem menghitung tahun berdasarkan tanggal
- Tahun akademik dimulai pada tanggal yang ditentukan
- Contoh: Jika start date = 1 Juli, maka:
  - Sebelum 1 Juli 2026 → Tahun akademik 2026 (26)
  - Setelah 1 Juli 2026 → Tahun akademik 2027 (27)

## 🔧 **Interface Admin**

### **1. Academic Year Settings (`academic_year_settings.php`)**

#### **Fitur:**
- ✅ Pengaturan tahun akademik saat ini
- ✅ Mode manual/otomatis
- ✅ Pengaturan gelombang pendaftaran
- ✅ Status pendaftaran aktif/nonaktif
- ✅ Konfigurasi auto mode (bulan & tanggal mulai)
- ✅ Quick transition tahun akademik
- ✅ Statistik pendaftar per tahun
- ✅ History transisi tahun

#### **Dashboard Cards:**
- **Tahun Akademik Saat Ini** - Menampilkan tahun aktif
- **Gelombang Saat Ini** - Gelombang pendaftaran aktif
- **Pendaftar Tahun Ini** - Jumlah pendaftar tahun aktif
- **Auto-Calculate Year** - Preview tahun otomatis

### **2. Year Migration Tools (`year_migration_tools.php`)**

#### **Fitur:**
- ✅ Bulk migration nomor pendaftaran antar tahun
- ✅ Preview migration sebelum eksekusi
- ✅ Statistik distribusi nomor per tahun
- ✅ History migration terbaru
- ✅ Logging semua perubahan

#### **Migration Process:**
1. **Preview** - Lihat data yang akan diubah
2. **Execute** - Jalankan migration dengan konfirmasi
3. **Logging** - Catat semua perubahan ke database
4. **Update Settings** - Update tahun akademik aktif

## 🔄 **Workflow Transisi Tahun**

### **Scenario 1: Manual Transition**
```
1. Admin masuk ke Academic Year Settings
2. Pilih "Transisi Tahun Akademik"
3. Input tahun baru (contoh: 27)
4. Tambahkan catatan (opsional)
5. Konfirmasi transisi
6. Sistem update:
   - Setting tahun akademik
   - Log ke history
   - Notifikasi sukses
```

### **Scenario 2: Bulk Migration**
```
1. Admin masuk ke Year Migration Tools
2. Pilih tahun asal dan tujuan
3. Preview migration
4. Execute migration
5. Sistem update:
   - Semua nomor pendaftaran
   - Setting tahun akademik
   - Log ke history dan registration_number_logs
```

### **Scenario 3: Auto Mode**
```
1. Admin set mode ke "auto"
2. Konfigurasi start date (contoh: 1 Juli)
3. Sistem otomatis menghitung tahun berdasarkan tanggal
4. Pendaftar baru otomatis dapat nomor tahun yang benar
```

## 📊 **Format Nomor Pendaftaran**

### **Dynamic Format:**
```
{PREFIX}{YEAR}.{WAVE}.{SEQUENCE}

Contoh:
- P27.2.001 (SMP, Tahun 2027, Gelombang 2, Urutan 1)
- A27.1.150 (SMA, Tahun 2027, Gelombang 1, Urutan 150)
```

### **Generation Logic:**
```php
function generate_registration_number($education_level, $wave = null) {
    $year = get_current_academic_year();        // Dari settings
    $wave = $wave ?? get_current_wave();        // Dari settings atau parameter
    $prefix = ($education_level == 'SMP') ? "P" : "A";
    
    // Get next sequence number
    $sequence = get_next_sequence($prefix, $year, $wave);
    
    return $prefix . $year . "." . $wave . "." . str_pad($sequence, 3, '0', STR_PAD_LEFT);
}
```

## 🛠️ **Tools & Testing**

### **1. Test Registration Format (`test_registration_format.php`)**
- Test academic year functions
- Test registration number generation
- Validate format patterns
- Check for duplicates
- Sequence generation testing

### **2. Group Features Test (`test_group_features.php`)**
- Integrated testing dengan fitur kelompok
- Test format nomor dalam konteks sistem

## 🔍 **Monitoring & Maintenance**

### **Regular Checks:**
1. **Weekly**: Verifikasi tahun akademik aktif
2. **Monthly**: Check auto-calculation accuracy
3. **Per Semester**: Review transition history
4. **Annual**: Plan year transition

### **Key Metrics:**
- Distribusi pendaftar per tahun
- Accuracy auto-calculation
- Migration success rate
- System performance

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **1. Wrong Academic Year**
```bash
# Check current settings
SELECT * FROM academic_year_settings WHERE setting_name = 'current_academic_year';

# Manual fix
UPDATE academic_year_settings 
SET setting_value = '27' 
WHERE setting_name = 'current_academic_year';
```

#### **2. Auto Mode Not Working**
```bash
# Check auto settings
SELECT * FROM academic_year_settings 
WHERE setting_name IN ('academic_year_mode', 'auto_year_start_month', 'auto_year_start_day');

# Test auto calculation
SELECT calculate_auto_academic_year();
```

#### **3. Migration Failed**
```bash
# Check migration history
SELECT * FROM academic_year_history ORDER BY transition_date DESC LIMIT 5;

# Check affected registrations
SELECT COUNT(*) FROM applicants WHERE registration_number LIKE 'P26.%' OR registration_number LIKE 'A26.%';
```

## 📈 **Future Enhancements**

### **Planned Features:**
1. **Automated Notifications** - Email alerts untuk transisi tahun
2. **Batch Operations** - Bulk update gelombang pendaftaran
3. **API Integration** - REST API untuk sistem eksternal
4. **Advanced Reporting** - Dashboard analytics
5. **Backup Integration** - Auto backup sebelum migration

### **Configuration Options:**
1. **Multiple Waves** - Support untuk lebih dari 9 gelombang
2. **Custom Formats** - Format nomor yang dapat dikustomisasi
3. **Regional Settings** - Support multiple timezone
4. **Validation Rules** - Custom validation untuk nomor pendaftaran

## 📞 **Support & Documentation**

### **Admin Resources:**
- `academic_year_settings.php` - Main configuration interface
- `year_migration_tools.php` - Migration and bulk operations
- `test_registration_format.php` - Testing and validation
- `SISTEM_TAHUN_AKADEMIK.md` - This documentation

### **Developer Resources:**
- `config.php` - Core functions
- `create_academic_year_settings.sql` - Database schema
- Database functions and procedures
- API documentation (if implemented)

---

**Last Updated**: 2025-01-12  
**Version**: 2.0  
**Status**: Production Ready
