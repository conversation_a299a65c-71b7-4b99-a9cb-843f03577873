<?php
// Test file untuk memverifikasi fitur kelompok
require_once "config.php";

echo "<h2>🧪 Test Fitur Kelompok</h2>";

// Test 1: Cek apakah semua file ada
echo "<h3>1. File Existence Test</h3>";
$files_to_check = [
    'admin_dashboard.php',
    'group_dashboard.php', 
    'group_view.php',
    'group_comparison.php'
];

foreach($files_to_check as $file) {
    if(file_exists($file)) {
        echo "✅ $file - EXISTS<br>";
    } else {
        echo "❌ $file - MISSING<br>";
    }
}

// Test 2: Cek koneksi database dan tabel groups
echo "<h3>2. Database Connection Test</h3>";
if($conn) {
    echo "✅ Database connection - OK<br>";
    
    // Test query groups
    $test_query = "SELECT COUNT(*) as count FROM groups";
    $result = mysqli_query($conn, $test_query);
    if($result) {
        $row = mysqli_fetch_assoc($result);
        echo "✅ Groups table accessible - " . $row['count'] . " groups found<br>";
    } else {
        echo "❌ Groups table - ERROR: " . mysqli_error($conn) . "<br>";
    }
} else {
    echo "❌ Database connection - FAILED<br>";
}

// Test 3: Cek query untuk mengambil data kelompok
echo "<h3>3. Groups Query Test</h3>";
$groups_query = "SELECT g.id, g.name, 
                 COUNT(CASE WHEN a.education_level = 'SMP' THEN 1 END) as smp_count,
                 COUNT(CASE WHEN a.education_level = 'SMA' THEN 1 END) as sma_count,
                 COUNT(a.id) as total_count
                 FROM groups g 
                 LEFT JOIN applicants a ON g.id = a.group_id 
                 GROUP BY g.id, g.name 
                 ORDER BY g.id";

$groups_result = mysqli_query($conn, $groups_query);
if($groups_result) {
    echo "✅ Groups query - SUCCESS<br>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>SMP</th><th>SMA</th><th>Total</th></tr>";
    
    while($group = mysqli_fetch_assoc($groups_result)) {
        echo "<tr>";
        echo "<td>" . $group['id'] . "</td>";
        echo "<td>" . htmlspecialchars($group['name']) . "</td>";
        echo "<td>" . $group['smp_count'] . "</td>";
        echo "<td>" . $group['sma_count'] . "</td>";
        echo "<td>" . $group['total_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Groups query - ERROR: " . mysqli_error($conn) . "<br>";
}

// Test 4: Cek fungsi permission
echo "<h3>4. Permission Functions Test</h3>";
if(function_exists('is_admin')) {
    echo "✅ is_admin() function - EXISTS<br>";
} else {
    echo "❌ is_admin() function - MISSING<br>";
}

if(function_exists('is_group_leader')) {
    echo "✅ is_group_leader() function - EXISTS<br>";
} else {
    echo "❌ is_group_leader() function - MISSING<br>";
}

// Test 5: Cek apakah status enum sudah diupdate
echo "<h3>5. Status Enum Test</h3>";
$status_query = "SHOW COLUMNS FROM applicants LIKE 'status'";
$status_result = mysqli_query($conn, $status_query);
if($status_result) {
    $status_info = mysqli_fetch_assoc($status_result);
    $enum_values = $status_info['Type'];
    
    if(strpos($enum_values, 'mengundurkan_diri') !== false) {
        echo "✅ Status enum updated - 'mengundurkan_diri' found<br>";
    } else {
        echo "❌ Status enum - 'mengundurkan_diri' not found<br>";
        echo "Current enum: " . $enum_values . "<br>";
    }
} else {
    echo "❌ Status enum check - ERROR<br>";
}

// Test 6: Cek apakah ada data dengan status mengundurkan_diri
echo "<h3>6. Status Data Test</h3>";
$status_data_query = "SELECT status, COUNT(*) as count FROM applicants GROUP BY status";
$status_data_result = mysqli_query($conn, $status_data_query);
if($status_data_result) {
    echo "✅ Status data query - SUCCESS<br>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Status</th><th>Count</th></tr>";
    
    while($status_data = mysqli_fetch_assoc($status_data_result)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($status_data['status']) . "</td>";
        echo "<td>" . $status_data['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Status data query - ERROR<br>";
}

echo "<h3>🎯 Test Summary</h3>";
echo "<p>Jika semua test menunjukkan ✅, maka fitur kelompok sudah siap digunakan!</p>";

echo "<h3>🔗 Quick Links untuk Testing</h3>";
echo "<ul>";
echo "<li><a href='admin_dashboard.php'>Admin Dashboard</a> - Test filter kelompok</li>";
echo "<li><a href='group_dashboard.php'>Group Dashboard</a> - Test navigasi kelompok</li>";
echo "<li><a href='group_view.php?group_id=1'>Group View (Kelompok 1)</a> - Test detail kelompok</li>";
echo "<li><a href='group_comparison.php'>Group Comparison</a> - Test perbandingan kelompok</li>";
echo "</ul>";

echo "<h3>📋 Checklist Manual Testing</h3>";
echo "<ol>";
echo "<li>Login sebagai admin, cek dropdown filter di admin dashboard</li>";
echo "<li>Login sebagai group leader, cek dropdown navigasi di group dashboard</li>";
echo "<li>Test filter kelompok di admin dashboard</li>";
echo "<li>Test navigasi ke kelompok lain dari group dashboard</li>";
echo "<li>Test halaman detail kelompok</li>";
echo "<li>Test halaman perbandingan kelompok</li>";
echo "<li>Test responsive design di mobile</li>";
echo "</ol>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}
h2, h3 {
    color: #333;
}
table {
    background-color: white;
    padding: 10px;
}
th {
    background-color: #0d6efd;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
ul, ol {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
