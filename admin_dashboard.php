<?php
// Include config file
require_once "config.php";
require_once "pagination_helper.php";

// Check if user is logged in and is admin
require_admin();

// Inisialisasi cache
$cache = get_cache();

// Define document types
$doc_types = array(
    'parent_permission_letter' => 'Surat Ijin Ortu',
    'birth_certificate' => 'Akta Kelahiran',
    'baptism_certificate' => 'Surat Baptis',
    'confirmation_certificate' => 'Surat Krisma',
    'family_card' => 'Kartu Keluarga',
    'parish_letter' => 'Rekom Paroki',
    'school_letter' => 'Rekom Sekolah',
    'parents_marriage_certificate' => 'Akta Pernikahan Ortu',
    'diploma' => 'Ijasah',
    'report_card' => 'Rapor',
    'questionnaire' => 'Kuesioner'
);

// Function to get missing documents for an applicant
function get_missing_documents($applicant_id) {
    global $conn, $doc_types, $cache;

    // Coba ambil dari cache
    $cacheKey = 'missing_docs_' . $applicant_id;
    $missing_doc_names = $cache->getCache($cacheKey);

    if ($missing_doc_names === false) {
        $missing_docs = array_keys($doc_types);

        $doc_sql = "SELECT document_type FROM documents WHERE applicant_id = ?";
        if($doc_stmt = mysqli_prepare($conn, $doc_sql)) {
            mysqli_stmt_bind_param($doc_stmt, "i", $applicant_id);

            if(mysqli_stmt_execute($doc_stmt)) {
                $doc_result = mysqli_stmt_get_result($doc_stmt);

                while($doc = mysqli_fetch_assoc($doc_result)) {
                    $index = array_search($doc['document_type'], $missing_docs);
                    if($index !== false) {
                        unset($missing_docs[$index]);
                    }
                }
            }

            mysqli_stmt_close($doc_stmt);
        }

        // Convert document types to readable names
        $missing_doc_names = [];
        foreach($missing_docs as $doc_type) {
            $missing_doc_names[] = $doc_types[$doc_type];
        }

        // Simpan ke cache
        $cache->setCache($cacheKey, $missing_doc_names);
    }

    return $missing_doc_names;
}

// Coba ambil statistik dashboard dari cache
$dashboardStatsKey = 'admin_dashboard_stats';
$dashboardStats = $cache->getCache($dashboardStatsKey);

if ($dashboardStats === false) {
    // Cache tidak ada atau expired, ambil dari database

    // Coba gunakan stored procedure jika tersedia
    $use_stored_procedure = false;

    try {
        // Cek apakah stored procedure tersedia
        $check_sp = "SHOW PROCEDURE STATUS WHERE Db = DATABASE() AND Name = 'GetDashboardStats'";
        $sp_result = mysqli_query($conn, $check_sp);

        if ($sp_result && mysqli_num_rows($sp_result) > 0) {
            $use_stored_procedure = true;
        }
    } catch (Exception $e) {
        // Jika error, gunakan query biasa
        $use_stored_procedure = false;
    }

    if ($use_stored_procedure) {
        // Gunakan stored procedure untuk statistik utama
        $stats_query = "CALL GetDashboardStats()";
        $stats_result = mysqli_query($conn, $stats_query);
        $stats = mysqli_fetch_assoc($stats_result);

        $smp_count = $stats['smp_count'];
        $sma_count = $stats['sma_count'];
        $total_applicants = $smp_count + $sma_count;
        $total_docs = $stats['total_docs'];
        $completed_interviews = $stats['completed_interviews'];

        // Free result
        mysqli_free_result($stats_result);

        // Jika ada multiple result sets, clear them
        while (mysqli_more_results($conn) && mysqli_next_result($conn)) {
            if ($result = mysqli_store_result($conn)) {
                mysqli_free_result($result);
            }
        }
    } else {
        // Gunakan query gabungan untuk statistik utama
        $stats_query = "SELECT
            (SELECT COUNT(*) FROM applicants WHERE education_level = 'SMP') as smp_count,
            (SELECT COUNT(*) FROM applicants WHERE education_level = 'SMA') as sma_count,
            (SELECT COUNT(*) FROM documents) as total_docs,
            (SELECT COUNT(*) FROM interviews WHERE status = 'completed') as completed_interviews";

        $stats_result = mysqli_query($conn, $stats_query);
        $stats = mysqli_fetch_assoc($stats_result);

        $smp_count = $stats['smp_count'];
        $sma_count = $stats['sma_count'];
        $total_applicants = $smp_count + $sma_count;
        $total_docs = $stats['total_docs'];
        $completed_interviews = $stats['completed_interviews'];
    }

    // Get waves statistics - Menggunakan indeks pada registration_number
    $wave_query = "SELECT
                    SUBSTRING_INDEX(SUBSTRING_INDEX(registration_number, '.', 2), '.', -1) as wave,
                    COUNT(*) as count
                  FROM applicants
                  GROUP BY wave
                  ORDER BY wave";
    $wave_result = mysqli_query($conn, $wave_query);
    $waves = [];
    while ($wave_row = mysqli_fetch_assoc($wave_result)) {
        $waves[$wave_row['wave']] = $wave_row['count'];
    }

    // Get status statistics - Menggunakan indeks pada status
    $status_query = "SELECT status, COUNT(*) as count FROM applicants GROUP BY status";
    $status_result = mysqli_query($conn, $status_query);
    $statuses = [];
    while ($status_row = mysqli_fetch_assoc($status_result)) {
        $statuses[$status_row['status']] = $status_row['count'];
    }

    // Get all groups for group filter
    $groups_query = "SELECT g.id, g.name,
                     COUNT(CASE WHEN a.education_level = 'SMP' THEN 1 END) as smp_count,
                     COUNT(CASE WHEN a.education_level = 'SMA' THEN 1 END) as sma_count,
                     COUNT(a.id) as total_count
                     FROM groups g
                     LEFT JOIN applicants a ON g.id = a.group_id
                     GROUP BY g.id, g.name
                     ORDER BY g.id";
    $groups_result = mysqli_query($conn, $groups_query);
    $all_groups = [];
    while ($group_row = mysqli_fetch_assoc($groups_result)) {
        $all_groups[] = $group_row;
    }

    // Simpan ke array
    $dashboardStats = [
        'smp_count' => $smp_count,
        'sma_count' => $sma_count,
        'total_applicants' => $total_applicants,
        'total_docs' => $total_docs,
        'completed_interviews' => $completed_interviews,
        'waves' => $waves,
        'statuses' => $statuses,
        'all_groups' => $all_groups
    ];

    // Simpan ke cache (berlaku 5 menit)
    $cache->setCache($dashboardStatsKey, $dashboardStats, 300);
} else {
    // Ambil data dari cache
    $smp_count = $dashboardStats['smp_count'];
    $sma_count = $dashboardStats['sma_count'];
    $total_applicants = $dashboardStats['total_applicants'];
    $total_docs = $dashboardStats['total_docs'];
    $completed_interviews = $dashboardStats['completed_interviews'];
    $waves = $dashboardStats['waves'];
    $statuses = $dashboardStats['statuses'];
    $all_groups = $dashboardStats['all_groups'];
}

// Pagination for SMP applicants
$smp_items_per_page = 10; // Number of items to display per page
$smp_page = isset($_GET['smp_page']) ? intval($_GET['smp_page']) : 1;

// Buat objek pagination untuk SMP
$smpPagination = new PaginationHelper($smp_count, $smp_items_per_page, $smp_page);

// Get SMP applicants with pagination - Query yang dioptimalkan dengan subquery
$smp_query = "SELECT a.*,
              IFNULL(d.doc_count, 0) AS doc_count,
              IFNULL(i.completed_interviews, 0) AS completed_interviews,
              IFNULL(i.total_interviews, 0) AS total_interviews,
              g.name AS group_name,
              gi.interviewer_name AS bidang4_interviewer
              FROM applicants a
              LEFT JOIN (
                  SELECT applicant_id, COUNT(*) as doc_count
                  FROM documents
                  GROUP BY applicant_id
              ) d ON a.id = d.applicant_id
              LEFT JOIN (
                  SELECT applicant_id,
                         COUNT(*) as total_interviews,
                         SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_interviews
                  FROM interviews
                  GROUP BY applicant_id
              ) i ON a.id = i.applicant_id
              LEFT JOIN groups g ON a.group_id = g.id
              LEFT JOIN interview_groups ig ON a.id = ig.applicant_id
              LEFT JOIN (
                  SELECT gi.group_id, gi.interviewer_name
                  FROM group_interviewers gi
                  WHERE gi.section_id = 4
              ) gi ON COALESCE(ig.group_id, a.group_id) = gi.group_id
              WHERE a.education_level = 'SMP'
              ORDER BY a.registration_number
              LIMIT ? OFFSET ?";

$smp_stmt = mysqli_prepare($conn, $smp_query);
$smp_limit = $smpPagination->getLimit();
$smp_offset = $smpPagination->getOffset();
mysqli_stmt_bind_param($smp_stmt, "ii", $smp_limit, $smp_offset);
mysqli_stmt_execute($smp_stmt);
$smp_result = mysqli_stmt_get_result($smp_stmt);

// Pagination for SMA applicants
$sma_items_per_page = 10; // Number of items to display per page
$sma_page = isset($_GET['sma_page']) ? intval($_GET['sma_page']) : 1;

// Buat objek pagination untuk SMA
$smaPagination = new PaginationHelper($sma_count, $sma_items_per_page, $sma_page);

// Get SMA applicants with pagination - Query yang dioptimalkan dengan subquery
$sma_query = "SELECT a.*,
              IFNULL(d.doc_count, 0) AS doc_count,
              IFNULL(i.completed_interviews, 0) AS completed_interviews,
              IFNULL(i.total_interviews, 0) AS total_interviews,
              g.name AS group_name,
              gi.interviewer_name AS bidang4_interviewer
              FROM applicants a
              LEFT JOIN (
                  SELECT applicant_id, COUNT(*) as doc_count
                  FROM documents
                  GROUP BY applicant_id
              ) d ON a.id = d.applicant_id
              LEFT JOIN (
                  SELECT applicant_id,
                         COUNT(*) as total_interviews,
                         SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_interviews
                  FROM interviews
                  GROUP BY applicant_id
              ) i ON a.id = i.applicant_id
              LEFT JOIN groups g ON a.group_id = g.id
              LEFT JOIN interview_groups ig ON a.id = ig.applicant_id
              LEFT JOIN (
                  SELECT gi.group_id, gi.interviewer_name
                  FROM group_interviewers gi
                  WHERE gi.section_id = 4
              ) gi ON COALESCE(ig.group_id, a.group_id) = gi.group_id
              WHERE a.education_level = 'SMA'
              ORDER BY a.registration_number
              LIMIT ? OFFSET ?";

$sma_stmt = mysqli_prepare($conn, $sma_query);
$sma_limit = $smaPagination->getLimit();
$sma_offset = $smaPagination->getOffset();
mysqli_stmt_bind_param($sma_stmt, "ii", $sma_limit, $sma_offset);
mysqli_stmt_execute($sma_stmt);
$sma_result = mysqli_stmt_get_result($sma_stmt);
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#2c3e50">
    <title>Admin Dashboard - Seminari Menengah St. Petrus Canisius</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="assets/css/mobile-optimized.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            padding: 15px 0;
            margin-bottom: 30px;
        }
        .logo img {
            width: 40px;
            height: auto;
            margin-right: 10px;
        }
        .dashboard-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .stats-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            transition: all 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stats-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #0d6efd;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            color: #6c757d;
        }
        .profile-pic {
            width: 45px;
            height: 45px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .profile-pic:hover {
            transform: scale(1.05);
        }
        .btn-action {
            padding: 2px 8px;
            font-size: 0.8rem;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 3px 8px;
        }
        .doc-badge {
            background-color: #ffc107;
            color: #000;
        }
        .wave-badge {
            background-color: #0dcaf0;
            color: #000;
            font-size: 0.75rem;
            padding: 2px 5px;
            border-radius: 3px;
            margin-left: 3px;
        }
        .interview-badge {
            background-color: #6f42c1;
            color: #fff;
            margin-left: 5px;
        }
        .nav-pills .nav-link {
            color: #495057;
            background-color: #f8f9fa;
            margin-right: 5px;
        }
        .nav-pills .nav-link.active {
            color: #fff;
            background-color: #0d6efd;
        }
        .search-bar {
            position: relative;
            margin-bottom: 20px;
        }
        .search-bar .bi-search {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #6c757d;
        }
        .search-bar input {
            padding-left: 35px;
        }
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        .table th {
            background-color: #f8f9fa;
            white-space: nowrap;
        }
        .applicant-info {
            white-space: nowrap;
        }
        .pagination {
            margin-bottom: 0;
        }
        .pagination .page-link {
            color: #0d6efd;
            border-color: #dee2e6;
        }
        .pagination .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }
        .pagination .page-item.disabled .page-link {
            color: #6c757d;
        }
        .group-filter-item.active {
            background-color: #0d6efd;
            color: white;
        }
        .group-filter-item.active .badge {
            background-color: rgba(255,255,255,0.2) !important;
        }
        .hidden-by-group-filter {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center logo">
                    <img src="assets/img/logo.png" alt="Logo Seminari">
                    <h5 class="mb-0">Admin Dashboard</h5>
                    <p class="mb-0 ms-3 text-muted">Seminari Menengah St. Petrus Canisius</p>
                </div>
                <div class="d-flex align-items-center">
                    <p class="mb-0 me-3">Welcome, <?php echo htmlspecialchars($_SESSION["name"]); ?></p>
                    <a href="change_password.php" class="btn btn-outline-secondary btn-sm me-2" title="Ubah Password">
                        <i class="bi bi-key"></i>
                    </a>
                    <div class="dropdown me-2">
    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" id="toolsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-tools me-1"></i>
        Tools
    </button>
    <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
        <li><a class="dropdown-item" href="update_registration_numbers.php"><i class="bi bi-123 me-2"></i>Update Nomor Pendaftaran</a></li>
        <li><a class="dropdown-item" href="import_registrations.php"><i class="bi bi-file-arrow-up me-2"></i>Import Pendaftar</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="manage_groups.php"><i class="bi bi-people me-2"></i>Kelola Kelompok</a></li>
        <li><a class="dropdown-item" href="manage_interviewers.php"><i class="bi bi-mic-fill me-2"></i>Kelola Pewawancara</a></li>
        <li><a class="dropdown-item" href="manage_users.php"><i class="bi bi-person-badge me-2"></i>Kelola User</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="export_data.php"><i class="bi bi-download me-2"></i>Ekspor Data</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="update_stats.php"><i class="bi bi-arrow-clockwise me-2"></i>Update Statistik</a></li>
        <li><a class="dropdown-item" href="cache_manager.php"><i class="bi bi-hdd-stack me-2"></i>Kelola Cache</a></li>
    </ul>
</div>
                    <a href="logout.php" class="btn btn-danger btn-sm">
                        <i class="bi bi-box-arrow-right me-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Dashboard Statistics -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">Statistik Dashboard</h5>
            <a href="cache_manager.php?action=clear_dashboard" class="btn btn-outline-secondary btn-sm" title="Refresh data dashboard">
                <i class="bi bi-arrow-clockwise me-1"></i>
                Refresh Data
            </a>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="bi bi-people-fill stats-icon"></i>
                    <div class="stats-number"><?php echo $total_applicants; ?></div>
                    <div class="stats-label">Total Pendaftar</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="bi bi-mortarboard-fill stats-icon"></i>
                    <div class="d-flex justify-content-center align-items-center">
                        <div class="stats-number me-2"><?php echo $smp_count; ?></div>
                        <div class="stats-number text-primary">/</div>
                        <div class="stats-number ms-2"><?php echo $sma_count; ?></div>
                    </div>
                    <div class="stats-label">Pendaftar SMP / SMA</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="bi bi-file-earmark-check-fill stats-icon"></i>
                    <div class="stats-number"><?php echo $total_docs; ?></div>
                    <div class="stats-label">Dokumen Terunggah</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="bi bi-chat-square-text-fill stats-icon"></i>
                    <div class="stats-number"><?php echo $completed_interviews; ?></div>
                    <div class="stats-label">Wawancara Selesai</div>
                </div>
            </div>
        </div>

        <!-- Wave Statistics -->
        <div class="dashboard-card mb-4">
            <h5 class="mb-3">Statistik Gelombang Pendaftaran</h5>
            <div class="row">
                <?php
                for ($i = 1; $i <= 2; $i++) {
                    $wave_count = isset($waves[$i]) ? $waves[$i] : 0;
                    $percentage = $total_applicants > 0 ? round(($wave_count / $total_applicants) * 100) : 0;
                ?>
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6 class="card-title">Gelombang <?php echo $i; ?></h6>
                            <div class="progress mb-2" style="height: 10px;">
                                <div class="progress-bar" role="progressbar" style="width: <?php echo $percentage; ?>%;"
                                     aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold"><?php echo $wave_count; ?> pendaftar</span>
                                <span class="text-muted"><?php echo $percentage; ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>

        <!-- Nav pills for SMP/SMA and Groups -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <ul class="nav nav-pills" id="pills-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <?php
                    // Determine active tab based on URL parameters
                    $active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'smp';
                    ?>
                    <button class="nav-link <?php echo ($active_tab == 'smp') ? 'active' : ''; ?>" id="pills-smp-tab" data-bs-toggle="pill" data-bs-target="#pills-smp" type="button" role="tab" aria-controls="pills-smp" aria-selected="<?php echo ($active_tab == 'smp') ? 'true' : 'false'; ?>">
                        <i class="bi bi-person-fill me-1"></i>
                        Pendaftar SMP <span class="badge bg-primary"><?php echo $smp_count; ?></span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link <?php echo ($active_tab == 'sma') ? 'active' : ''; ?>" id="pills-sma-tab" data-bs-toggle="pill" data-bs-target="#pills-sma" type="button" role="tab" aria-controls="pills-sma" aria-selected="<?php echo ($active_tab == 'sma') ? 'true' : 'false'; ?>">
                        <i class="bi bi-mortarboard-fill me-1"></i>
                        Pendaftar SMA <span class="badge bg-primary"><?php echo $sma_count; ?></span>
                    </button>
                </li>
            </ul>

            <!-- Group Filter Dropdown -->
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" id="groupFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-people-fill me-1"></i>
                    <span id="selectedGroupText">Semua Kelompok</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="groupFilterDropdown">
                    <li><a class="dropdown-item group-filter-item active" href="#" data-group="all">
                        <i class="bi bi-list me-2"></i>Semua Kelompok
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <?php foreach($all_groups as $group): ?>
                    <li><a class="dropdown-item group-filter-item" href="#" data-group="<?php echo $group['id']; ?>">
                        <i class="bi bi-funnel me-2"></i>Filter <?php echo htmlspecialchars($group['name']); ?>
                        <span class="badge bg-secondary ms-2"><?php echo $group['total_count']; ?></span>
                    </a></li>
                    <?php endforeach; ?>
                    <li><hr class="dropdown-divider"></li>
                    <li class="dropdown-header">Lihat Detail Kelompok</li>
                    <?php foreach($all_groups as $group): ?>
                    <li><a class="dropdown-item" href="group_view.php?group_id=<?php echo $group['id']; ?>">
                        <i class="bi bi-eye me-2"></i><?php echo htmlspecialchars($group['name']); ?>
                        <span class="badge bg-primary ms-2"><?php echo $group['total_count']; ?></span>
                    </a></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>

        <!-- Tab content -->
        <div class="tab-content" id="pills-tabContent">
            <!-- SMP Applicants -->
            <div class="tab-pane fade <?php echo ($active_tab == 'smp') ? 'show active' : ''; ?>" id="pills-smp" role="tabpanel" aria-labelledby="pills-smp-tab">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">Daftar Pendaftar SMP</h5>
                        <div class="d-flex">
                            <div class="dropdown me-2">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-funnel me-1"></i>
                                    Filter
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="filterDropdown">
                                    <li><a class="dropdown-item" href="#">Semua</a></li>
                                    <li><a class="dropdown-item" href="#">Dokumen Lengkap</a></li>
                                    <li><a class="dropdown-item" href="#">Dokumen Belum Lengkap</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#">Gelombang 1</a></li>
                                    <li><a class="dropdown-item" href="#">Gelombang 2</a></li>
                                </ul>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-sort-alpha-down me-1"></i>
                                    Urutkan
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                    <li><a class="dropdown-item" href="#">No. Pendaftaran</a></li>
                                    <li><a class="dropdown-item" href="#">Nama</a></li>
                                    <li><a class="dropdown-item" href="#">Asal Sekolah</a></li>
                                    <li><a class="dropdown-item" href="#">Asal Paroki</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" class="form-control" id="searchSmp" placeholder="Cari pendaftar SMP...">
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-mobile-friendly">
                            <thead>
                                <tr>
                                    <th>NO. PENDAFTARAN</th>
                                    <th>NAMA</th>
                                    <th class="mobile-hide">ASAL SEKOLAH</th>
                                    <th class="mobile-hide">PAROKI</th>
                                    <th>DOKUMEN</th>
                                    <th class="mobile-hide">WAWANCARA</th>
                                    <th>STATUS</th>
                                    <th>AKSI</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($smp_count == 0): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">Belum ada pendaftar SMP</td>
                                </tr>
                                <?php else: ?>
                                <?php while($row = mysqli_fetch_assoc($smp_result)): ?>
                                <tr data-group-id="<?php echo $row['group_id'] ? $row['group_id'] : 'unassigned'; ?>">
                                    <td data-label="NO. PENDAFTARAN">
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <?php
                                                $reg_number = htmlspecialchars($row['registration_number']);
                                                $wave = get_wave_from_registration_number($reg_number);
                                                echo $reg_number;
                                                echo '<span class="wave-badge">Gel. ' . $wave . '</span>';
                                                ?>
                                                <div style="font-size: 0.7rem; color: #6c757d; margin-top: 2px;">
                                                    <?php
                                                    // Tampilkan kelompok asli
                                                    if (!empty($row['group_name'])) {
                                                        echo '<span class="me-1" title="Kelompok Asli"><i class="bi bi-people-fill me-1"></i>' . htmlspecialchars($row['group_name']) . '</span>';
                                                    }

                                                    // Tampilkan pewawancara bidang 4
                                                    if (!empty($row['bidang4_interviewer'])) {
                                                        echo '<span class="ms-1" title="Pewawancara Bidang 4">Bidang 4: ' . htmlspecialchars($row['bidang4_interviewer']) . '</span>';
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                            <a href="edit_registration_number.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-link text-secondary p-0 ms-2" title="Edit Nomor Pendaftaran">
                                                <i class="bi bi-pencil-square"></i>
                                            </a>
                                        </div>
                                    </td>
                                    <td data-label="NAMA">
                                        <div class="d-flex align-items-center">
                                            <?php if(!empty($row['photo_path'])): ?>
                                            <div class="me-2" style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); padding: 2px; border-radius: 10px; cursor: pointer;" onclick="openImageModal('<?php echo htmlspecialchars($row['photo_path']); ?>', '<?php echo htmlspecialchars($row['name']); ?>')">
                                                <img src="<?php echo htmlspecialchars($row['photo_path']); ?>" class="profile-pic">
                                            </div>
                                            <?php else: ?>
                                            <div class="profile-pic me-2 bg-secondary d-flex align-items-center justify-content-center" style="border-radius: 8px; border: 2px solid #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                                <i class="bi bi-person-fill text-white"></i>
                                            </div>
                                            <?php endif; ?>
                                            <div>
                                                <p class="mb-0 fw-bold"><?php echo htmlspecialchars($row['name']); ?></p>
                                                <small class="text-muted"><?php echo htmlspecialchars($row['birth_place'] . ', ' . date('d/m/Y', strtotime($row['birth_date']))); ?></small>
                                                <small class="d-block d-md-none text-muted"><?php echo htmlspecialchars($row['school_origin']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['school_origin']); ?></td>
                                    <td><?php echo htmlspecialchars($row['parish_origin']); ?></td>
                                    <td>
                                        <span class="badge doc-badge">
                                            <i class="bi bi-files me-1"></i>
                                            <?php echo $row['doc_count']; ?>/11
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge interview-badge">
                                            <i class="bi bi-chat-dots me-1"></i>
                                            <?php echo $row['completed_interviews']; ?>/<?php echo $row['total_interviews']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if($row['status'] == 'pending'):
                                            $missing_docs = get_missing_documents($row['id']);
                                            $missing_docs_text = implode(", ", $missing_docs);
                                        ?>
                                        <span class="badge bg-warning status-badge" data-bs-toggle="tooltip" data-bs-html="true" title="Dokumen yang belum diupload:<br><?php echo $missing_docs_text; ?>">Belum Lengkap <i class="bi bi-info-circle-fill"></i></span>
                                        <?php elseif($row['status'] == 'completed'): ?>
                                        <span class="badge bg-success status-badge">Lengkap</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger status-badge">Mengundurkan diri</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="applicant_detail.php?id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm btn-action">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="edit_data.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm btn-action">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="applicant_edit.php?id=<?php echo $row['id']; ?>" class="btn btn-success btn-sm btn-action">
                                                <i class="bi bi-arrow-left-right"></i>
                                            </a>
                                            <a href="applicant_delete.php?id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm btn-action" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- SMP Pagination -->
                    <?php if ($smp_count > 0): ?>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="mb-0 text-muted">Menampilkan <?php echo min($smp_count, $smpPagination->getLimit()); ?> dari <?php echo $smp_count; ?> pendaftar</p>
                        </div>
                        <?php if ($smpPagination->getTotalPages() > 1): ?>
                        <?php
                            // Tambahkan parameter tambahan untuk tab dan halaman SMA
                            $additionalParams = ['tab' => 'smp'];
                            if (isset($_GET['sma_page'])) {
                                $additionalParams['sma_page'] = $_GET['sma_page'];
                            }
                            echo $smpPagination->renderPagination($_SERVER['PHP_SELF'], 'smp_page', $additionalParams);
                        ?>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- SMA Applicants -->
            <div class="tab-pane fade <?php echo ($active_tab == 'sma') ? 'show active' : ''; ?>" id="pills-sma" role="tabpanel" aria-labelledby="pills-sma-tab">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0">Daftar Pendaftar SMA</h5>
                        <div class="d-flex">
                            <div class="dropdown me-2">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="filterDropdownSma" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-funnel me-1"></i>
                                    Filter
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="filterDropdownSma">
                                    <li><a class="dropdown-item" href="#">Semua</a></li>
                                    <li><a class="dropdown-item" href="#">Dokumen Lengkap</a></li>
                                    <li><a class="dropdown-item" href="#">Dokumen Belum Lengkap</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#">Gelombang 1</a></li>
                                    <li><a class="dropdown-item" href="#">Gelombang 2</a></li>
                                </ul>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="sortDropdownSma" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-sort-alpha-down me-1"></i>
                                    Urutkan
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="sortDropdownSma">
                                    <li><a class="dropdown-item" href="#">No. Pendaftaran</a></li>
                                    <li><a class="dropdown-item" href="#">Nama</a></li>
                                    <li><a class="dropdown-item" href="#">Asal Sekolah</a></li>
                                    <li><a class="dropdown-item" href="#">Asal Paroki</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" class="form-control" id="searchSma" placeholder="Cari pendaftar SMA...">
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-mobile-friendly">
                            <thead>
                                <tr>
                                    <th>NO. PENDAFTARAN</th>
                                    <th>NAMA</th>
                                    <th class="mobile-hide">ASAL SEKOLAH</th>
                                    <th class="mobile-hide">PAROKI</th>
                                    <th>DOKUMEN</th>
                                    <th class="mobile-hide">WAWANCARA</th>
                                    <th>STATUS</th>
                                    <th>AKSI</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($sma_count == 0): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">Belum ada pendaftar SMA</td>
                                </tr>
                                <?php else: ?>
                                <?php while($row = mysqli_fetch_assoc($sma_result)): ?>
                                <tr data-group-id="<?php echo $row['group_id'] ? $row['group_id'] : 'unassigned'; ?>">
                                    <td data-label="NO. PENDAFTARAN">
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <?php
                                                $reg_number = htmlspecialchars($row['registration_number']);
                                                $wave = get_wave_from_registration_number($reg_number);
                                                echo $reg_number;
                                                echo '<span class="wave-badge">Gel. ' . $wave . '</span>';
                                                ?>
                                                <div style="font-size: 0.7rem; color: #6c757d; margin-top: 2px;">
                                                    <?php
                                                    // Tampilkan kelompok asli
                                                    if (!empty($row['group_name'])) {
                                                        echo '<span class="me-1" title="Kelompok Asli"><i class="bi bi-people-fill me-1"></i>' . htmlspecialchars($row['group_name']) . '</span>';
                                                    }

                                                    // Tampilkan pewawancara bidang 4
                                                    if (!empty($row['bidang4_interviewer'])) {
                                                        echo '<span class="ms-1" title="Pewawancara Bidang 4">Bidang 4: ' . htmlspecialchars($row['bidang4_interviewer']) . '</span>';
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                            <a href="edit_registration_number.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-link text-secondary p-0 ms-2" title="Edit Nomor Pendaftaran">
                                                <i class="bi bi-pencil-square"></i>
                                            </a>
                                        </div>
                                    </td>
                                    <td data-label="NAMA">
                                        <div class="d-flex align-items-center">
                                            <?php if(!empty($row['photo_path'])): ?>
                                            <div class="me-2" style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); padding: 2px; border-radius: 10px; cursor: pointer;" onclick="openImageModal('<?php echo htmlspecialchars($row['photo_path']); ?>', '<?php echo htmlspecialchars($row['name']); ?>')">
                                                <img src="<?php echo htmlspecialchars($row['photo_path']); ?>" class="profile-pic">
                                            </div>
                                            <?php else: ?>
                                            <div class="profile-pic me-2 bg-secondary d-flex align-items-center justify-content-center" style="border-radius: 8px; border: 2px solid #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                                <i class="bi bi-person-fill text-white"></i>
                                            </div>
                                            <?php endif; ?>
                                            <div>
                                                <p class="mb-0 fw-bold"><?php echo htmlspecialchars($row['name']); ?></p>
                                                <small class="text-muted"><?php echo htmlspecialchars($row['birth_place'] . ', ' . date('d/m/Y', strtotime($row['birth_date']))); ?></small>
                                                <small class="d-block d-md-none text-muted"><?php echo htmlspecialchars($row['school_origin']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['school_origin']); ?></td>
                                    <td><?php echo htmlspecialchars($row['parish_origin']); ?></td>
                                    <td>
                                        <span class="badge doc-badge">
                                            <i class="bi bi-files me-1"></i>
                                            <?php echo $row['doc_count']; ?>/11
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge interview-badge">
                                            <i class="bi bi-chat-dots me-1"></i>
                                            <?php echo $row['completed_interviews']; ?>/<?php echo $row['total_interviews']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if($row['status'] == 'pending'):
                                            $missing_docs = get_missing_documents($row['id']);
                                            $missing_docs_text = implode(", ", $missing_docs);
                                        ?>
                                        <span class="badge bg-warning status-badge" data-bs-toggle="tooltip" data-bs-html="true" title="Dokumen yang belum diupload:<br><?php echo $missing_docs_text; ?>">Belum Lengkap <i class="bi bi-info-circle-fill"></i></span>
                                        <?php elseif($row['status'] == 'completed'): ?>
                                        <span class="badge bg-success status-badge">Lengkap</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger status-badge">Mengundurkan diri</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="applicant_detail.php?id=<?php echo $row['id']; ?>" class="btn btn-primary btn-sm btn-action">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="edit_data.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm btn-action">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="applicant_edit.php?id=<?php echo $row['id']; ?>" class="btn btn-success btn-sm btn-action">
                                                <i class="bi bi-arrow-left-right"></i>
                                            </a>
                                            <a href="applicant_delete.php?id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm btn-action" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- SMA Pagination -->
                    <?php if ($sma_count > 0): ?>
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <p class="mb-0 text-muted">Menampilkan <?php echo min($sma_count, $smaPagination->getLimit()); ?> dari <?php echo $sma_count; ?> pendaftar</p>
                        </div>
                        <?php if ($smaPagination->getTotalPages() > 1): ?>
                        <?php
                            // Tambahkan parameter tambahan untuk tab dan halaman SMP
                            $additionalParams = ['tab' => 'sma'];
                            if (isset($_GET['smp_page'])) {
                                $additionalParams['smp_page'] = $_GET['smp_page'];
                            }
                            echo $smaPagination->renderPagination($_SERVER['PHP_SELF'], 'sma_page', $additionalParams);
                        ?>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Foto Pendaftar</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="Foto Pendaftar" class="img-fluid" style="max-height: 70vh; border-radius: 8px;">
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; <?php echo date("Y"); ?> Seminari Menengah St. Petrus Canisius</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    placement: 'top',
                    trigger: 'hover focus',
                    html: true
                })
            })
        });

        function openImageModal(imageSrc, name) {
            document.getElementById('modalImage').src = imageSrc;
            document.getElementById('imageModalLabel').textContent = 'Foto ' + name;
            var imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
            imageModal.show();
        }
        // Tab navigation with pagination preservation
        document.getElementById('pills-smp-tab').addEventListener('click', function() {
            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.set('tab', 'smp');

            // Update URL without reloading the page
            const newUrl = window.location.pathname + '?' + urlParams.toString();
            history.pushState({}, '', newUrl);
        });

        document.getElementById('pills-sma-tab').addEventListener('click', function() {
            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.set('tab', 'sma');

            // Update URL without reloading the page
            const newUrl = window.location.pathname + '?' + urlParams.toString();
            history.pushState({}, '', newUrl);
        });

        // Simple search functionality
        document.getElementById('searchSmp').addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const smpTable = document.querySelector('#pills-smp table tbody');
            const rows = smpTable.querySelectorAll('tr');
            let visibleCount = 0;

            rows.forEach(row => {
                // Skip if row is hidden by group filter
                if (row.classList.contains('hidden-by-group-filter')) {
                    return;
                }

                const text = row.textContent.toLowerCase();
                if(text.indexOf(searchText) > -1) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Hide pagination when searching
            const paginationElement = document.querySelector('#pills-smp .pagination');
            if (paginationElement) {
                paginationElement.parentElement.style.display = searchText ? 'none' : '';
            }
        });

        document.getElementById('searchSma').addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const smaTable = document.querySelector('#pills-sma table tbody');
            const rows = smaTable.querySelectorAll('tr');
            let visibleCount = 0;

            rows.forEach(row => {
                // Skip if row is hidden by group filter
                if (row.classList.contains('hidden-by-group-filter')) {
                    return;
                }

                const text = row.textContent.toLowerCase();
                if(text.indexOf(searchText) > -1) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Hide pagination when searching
            const paginationElement = document.querySelector('#pills-sma .pagination');
            if (paginationElement) {
                paginationElement.parentElement.style.display = searchText ? 'none' : '';
            }
        });

        // Group filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const groupFilterItems = document.querySelectorAll('.group-filter-item');
            const selectedGroupText = document.getElementById('selectedGroupText');

            groupFilterItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all items
                    groupFilterItems.forEach(i => i.classList.remove('active'));

                    // Add active class to clicked item
                    this.classList.add('active');

                    // Update selected text
                    const groupId = this.getAttribute('data-group');
                    const groupText = this.textContent.trim();
                    selectedGroupText.textContent = groupText.split(' ')[0] + ' ' + groupText.split(' ')[1]; // Remove badge text

                    // Filter table rows
                    filterByGroup(groupId);
                });
            });
        });

        function filterByGroup(groupId) {
            // Get all table rows in both SMP and SMA tables
            const smpRows = document.querySelectorAll('#pills-smp tbody tr');
            const smaRows = document.querySelectorAll('#pills-sma tbody tr');

            // Combine both sets of rows
            const allRows = [...smpRows, ...smaRows];

            allRows.forEach(row => {
                // Skip header rows and empty message rows
                if (row.cells.length <= 1 || row.querySelector('th')) {
                    return;
                }

                const rowGroupId = row.getAttribute('data-group-id');

                if (groupId === 'all') {
                    // Show all rows
                    row.classList.remove('hidden-by-group-filter');
                } else {
                    // Show only rows matching the selected group
                    if (rowGroupId === groupId) {
                        row.classList.remove('hidden-by-group-filter');
                    } else {
                        row.classList.add('hidden-by-group-filter');
                    }
                }
            });

            // Update pagination visibility
            updatePaginationVisibility();
        }

        function updatePaginationVisibility() {
            // Hide pagination when filtering by group (except "all")
            const selectedGroup = document.querySelector('.group-filter-item.active').getAttribute('data-group');
            const paginationElements = document.querySelectorAll('.pagination');

            paginationElements.forEach(pagination => {
                if (selectedGroup === 'all') {
                    pagination.parentElement.style.display = '';
                } else {
                    pagination.parentElement.style.display = 'none';
                }
            });
        }
    </script>

    <!-- Include Mobile Navigation -->
    <?php include 'includes/mobile_nav.php'; ?>
</body>
</html>