<?php
// Include config file
require_once "config.php";

// Check if user is logged in and is admin
require_admin();

$update_success = false;
$update_count = 0;
$error_message = "";

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["update_numbers"])) {
    $wave = isset($_POST["wave"]) ? intval($_POST["wave"]) : 2;
    
    // Start transaction
    mysqli_begin_transaction($conn);
    
    try {
        // Get all applicants
        $sql = "SELECT id, registration_number, education_level FROM applicants ORDER BY id";
        $result = mysqli_query($conn, $sql);
        
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $id = $row['id'];
                $old_reg_number = $row['registration_number'];
                $education_level = $row['education_level'];
                
                // Extract the sequence number from old registration number
                $sequence = 0;
                if (preg_match('/\.(\d+)$/', $old_reg_number, $matches)) {
                    $sequence = intval($matches[1]);
                }
                
                // Generate new registration number with wave
                $year = substr($old_reg_number, 1, 2); // Extract year from existing number
                $prefix = ($education_level == 'SMP') ? "P" : "A";
                $new_reg_number = $prefix . $year . "." . $wave . "." . str_pad($sequence, 3, '0', STR_PAD_LEFT);
                
                // Update the registration number
                $update_sql = "UPDATE applicants SET registration_number = ? WHERE id = ?";
                $stmt = mysqli_prepare($conn, $update_sql);
                mysqli_stmt_bind_param($stmt, "si", $new_reg_number, $id);
                
                if (mysqli_stmt_execute($stmt)) {
                    $update_count++;
                    
                    // Log the change
                    $log_sql = "INSERT INTO registration_number_logs (applicant_id, old_number, new_number, changed_by) VALUES (?, ?, ?, ?)";
                    $log_stmt = mysqli_prepare($conn, $log_sql);
                    if ($log_stmt) {
                        $user_id = $_SESSION['user_id'];
                        mysqli_stmt_bind_param($log_stmt, "issi", $id, $old_reg_number, $new_reg_number, $user_id);
                        mysqli_stmt_execute($log_stmt);
                        mysqli_stmt_close($log_stmt);
                    }
                }
                
                mysqli_stmt_close($stmt);
            }
            
            mysqli_commit($conn);
            $update_success = true;
        }
    } catch (Exception $e) {
        mysqli_rollback($conn);
        $error_message = "Error updating registration numbers: " . $e->getMessage();
    }
}

// Get current registration number format examples
$smp_example = "";
$sma_example = "";

$sql = "SELECT registration_number FROM applicants WHERE education_level = 'SMP' LIMIT 1";
$result = mysqli_query($conn, $sql);
if ($row = mysqli_fetch_assoc($result)) {
    $smp_example = $row['registration_number'];
}

$sql = "SELECT registration_number FROM applicants WHERE education_level = 'SMA' LIMIT 1";
$result = mysqli_query($conn, $sql);
if ($row = mysqli_fetch_assoc($result)) {
    $sma_example = $row['registration_number'];
}

// Count total applicants
$count_sql = "SELECT COUNT(*) as total FROM applicants";
$count_result = mysqli_query($conn, $count_sql);
$total_applicants = 0;
if ($count_row = mysqli_fetch_assoc($count_result)) {
    $total_applicants = $count_row['total'];
}

// Create registration_number_logs table if it doesn't exist
$check_table = mysqli_query($conn, "SHOW TABLES LIKE 'registration_number_logs'");
if(mysqli_num_rows($check_table) == 0) {
    $create_logs_table = "
    CREATE TABLE registration_number_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        applicant_id INT NOT NULL,
        old_number VARCHAR(20) NOT NULL,
        new_number VARCHAR(20) NOT NULL,
        changed_by INT,
        changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (applicant_id) REFERENCES applicants(id) ON DELETE CASCADE,
        FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE SET NULL
    )";
    mysqli_query($conn, $create_logs_table);
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Format Nomor Pendaftaran - Seminari Menengah St. Petrus Canisius</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            padding: 15px 0;
            margin-bottom: 30px;
        }
        .logo img {
            width: 40px;
            height: auto;
            margin-right: 10px;
        }
        .main-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .format-example {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center logo">
                    <img src="assets/img/logo.png" alt="Logo Seminari">
                    <h5 class="mb-0">Update Format Nomor Pendaftaran</h5>
                    <p class="mb-0 ms-3 text-muted">Seminari Menengah St. Petrus Canisius</p>
                </div>
                <div>
                    <a href="admin_dashboard.php" class="btn btn-outline-primary btn-sm">← Kembali ke Dashboard</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="main-card">
            <h4 class="mb-4">Update Format Nomor Pendaftaran</h4>
            
            <?php if($update_success): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle-fill me-2"></i>
                Berhasil mengupdate <?php echo $update_count; ?> nomor pendaftaran!
            </div>
            <?php endif; ?>
            
            <?php if(!empty($error_message)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Format Nomor Pendaftaran</h5>
                </div>
                <div class="card-body">
                    <p>Format nomor pendaftaran yang baru adalah: <strong>P26.2.001</strong></p>
                    <ul>
                        <li><strong>P/A</strong> - Jenjang pendidikan (P untuk SMP, A untuk SMA)</li>
                        <li><strong>26</strong> - Tahun pelajaran 2026</li>
                        <li><strong>2</strong> - Gelombang pendaftaran</li>
                        <li><strong>001</strong> - Urutan pendaftaran</li>
                    </ul>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6>Format Saat Ini:</h6>
                            <?php if(!empty($smp_example)): ?>
                            <div class="format-example">
                                SMP: <?php echo $smp_example; ?>
                            </div>
                            <?php endif; ?>
                            
                            <?php if(!empty($sma_example)): ?>
                            <div class="format-example">
                                SMA: <?php echo $sma_example; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <h6>Format Baru (contoh):</h6>
                            <div class="format-example">
                                SMP: P25.2.001
                            </div>
                            <div class="format-example">
                                SMA: A25.2.001
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Update Semua Nomor Pendaftaran</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>Perhatian!</strong> Proses ini akan mengubah format nomor pendaftaran untuk semua pendaftar (<?php echo $total_applicants; ?> orang).
                        Pastikan Anda telah mencadangkan data sebelum melanjutkan.
                    </div>
                    
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                        <div class="mb-3">
                            <label for="wave" class="form-label">Pilih Gelombang Pendaftaran</label>
                            <select name="wave" class="form-select">
                                <option value="1">Gelombang 1</option>
                                <option value="2" selected>Gelombang 2</option>
                                <option value="3">Gelombang 3</option>
                            </select>
                            <div class="form-text">Semua pendaftar akan diubah ke gelombang yang dipilih.</div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" name="update_numbers" class="btn btn-warning" onclick="return confirm('Apakah Anda yakin ingin mengubah semua nomor pendaftaran?');">
                                <i class="bi bi-arrow-repeat me-2"></i>
                                Update Semua Nomor Pendaftaran
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; <?php echo date("Y"); ?> Seminari Menengah St. Petrus Canisius</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>