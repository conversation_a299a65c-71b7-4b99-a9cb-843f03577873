<?php
// Include config file
require_once "config.php";

// Check if user is logged in and is admin
require_admin();

// Define variables and initialize with empty values
$registration_number = "";
$registration_number_err = "";
$success_message = "";
$error_message = "";
$applicant_id = 0;
$applicant_name = "";
$old_registration_number = "";
$education_level = "";

// Check if ID parameter is set
if (!isset($_GET["id"]) || empty(trim($_GET["id"]))) {
    header("location: admin_dashboard.php");
    exit;
}

// Get applicant ID
$applicant_id = trim($_GET["id"]);

// Prepare a select statement to get applicant details
$sql = "SELECT id, name, registration_number, education_level FROM applicants WHERE id = ?";

if ($stmt = mysqli_prepare($conn, $sql)) {
    // Bind variables to the prepared statement as parameters
    mysqli_stmt_bind_param($stmt, "i", $param_id);
    
    // Set parameters
    $param_id = $applicant_id;
    
    // Attempt to execute the prepared statement
    if (mysqli_stmt_execute($stmt)) {
        $result = mysqli_stmt_get_result($stmt);
        
        if (mysqli_num_rows($result) == 1) {
            // Fetch result row as an associative array
            $row = mysqli_fetch_array($result, MYSQLI_ASSOC);
            
            // Retrieve values
            $applicant_name = $row["name"];
            $old_registration_number = $row["registration_number"];
            $registration_number = $old_registration_number;
            $education_level = $row["education_level"];
        } else {
            // URL doesn't contain valid id parameter
            header("location: admin_dashboard.php");
            exit;
        }
    } else {
        $error_message = "Oops! Something went wrong. Please try again later.";
    }
    
    // Close statement
    mysqli_stmt_close($stmt);
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate registration number
    $input_registration_number = trim($_POST["registration_number"]);
    if (empty($input_registration_number)) {
        $registration_number_err = "Silakan masukkan nomor pendaftaran.";
    } elseif (!preg_match('/^[A-Z]\d{2}\.\d\.\d{3}$/', $input_registration_number)) {
        $registration_number_err = "Format nomor pendaftaran tidak valid. Gunakan format: P26.2.001";
    } else {
        // Check if registration number already exists (except for the current applicant)
        $check_sql = "SELECT id FROM applicants WHERE registration_number = ? AND id != ?";
        if ($check_stmt = mysqli_prepare($conn, $check_sql)) {
            mysqli_stmt_bind_param($check_stmt, "si", $input_registration_number, $applicant_id);
            mysqli_stmt_execute($check_stmt);
            mysqli_stmt_store_result($check_stmt);
            
            if (mysqli_stmt_num_rows($check_stmt) > 0) {
                $registration_number_err = "Nomor pendaftaran ini sudah digunakan oleh pendaftar lain.";
            } else {
                $registration_number = $input_registration_number;
            }
            
            mysqli_stmt_close($check_stmt);
        }
    }
    
    // Check input errors before updating in database
    if (empty($registration_number_err)) {
        // Start transaction
        mysqli_begin_transaction($conn);
        
        try {
            // Prepare an update statement
            $update_sql = "UPDATE applicants SET registration_number = ? WHERE id = ?";
            
            if ($update_stmt = mysqli_prepare($conn, $update_sql)) {
                // Bind variables to the prepared statement as parameters
                mysqli_stmt_bind_param($update_stmt, "si", $registration_number, $applicant_id);
                
                // Attempt to execute the prepared statement
                if (mysqli_stmt_execute($update_stmt)) {
                    // Log the change
                    $log_sql = "INSERT INTO registration_number_logs (applicant_id, old_number, new_number, changed_by) VALUES (?, ?, ?, ?)";
                    $log_stmt = mysqli_prepare($conn, $log_sql);
                    
                    if ($log_stmt) {
                        $user_id = $_SESSION['user_id'];
                        mysqli_stmt_bind_param($log_stmt, "issi", $applicant_id, $old_registration_number, $registration_number, $user_id);
                        mysqli_stmt_execute($log_stmt);
                        mysqli_stmt_close($log_stmt);
                    }
                    
                    // Commit transaction
                    mysqli_commit($conn);
                    
                    // Set success message
                    $success_message = "Nomor pendaftaran berhasil diubah dari " . $old_registration_number . " menjadi " . $registration_number;
                    
                    // Update old registration number for display
                    $old_registration_number = $registration_number;
                } else {
                    throw new Exception("Terjadi kesalahan saat mengupdate nomor pendaftaran.");
                }
                
                // Close statement
                mysqli_stmt_close($update_stmt);
            }
        } catch (Exception $e) {
            // Rollback transaction on error
            mysqli_rollback($conn);
            $error_message = $e->getMessage();
        }
    }
}

// Get registration number change history
$history = array();
$history_sql = "SELECT l.*, u.name as changed_by_name 
                FROM registration_number_logs l 
                LEFT JOIN users u ON l.changed_by = u.id 
                WHERE l.applicant_id = ? 
                ORDER BY l.changed_at DESC";

if ($history_stmt = mysqli_prepare($conn, $history_sql)) {
    mysqli_stmt_bind_param($history_stmt, "i", $applicant_id);
    mysqli_stmt_execute($history_stmt);
    $history_result = mysqli_stmt_get_result($history_stmt);
    
    while ($row = mysqli_fetch_assoc($history_result)) {
        $history[] = $row;
    }
    
    mysqli_stmt_close($history_stmt);
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Nomor Pendaftaran - Seminari Menengah St. Petrus Canisius</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 15px 0;
            margin-bottom: 30px;
        }
        .logo img {
            width: 40px;
            height: auto;
            margin-right: 10px;
        }
        .main-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .history-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center logo">
                    <img src="assets/img/logo.png" alt="Logo Seminari">
                    <h5 class="mb-0">Edit Nomor Pendaftaran</h5>
                </div>
                <div>
                    <a href="applicant_detail.php?id=<?php echo $applicant_id; ?>" class="btn btn-light btn-sm">
                        <i class="bi bi-arrow-left me-1"></i> Kembali ke Detail Pendaftar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="main-card">
            <h4 class="mb-4">Edit Nomor Pendaftaran</h4>
            
            <?php if (!empty($success_message)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle-fill me-2"></i>
                <?php echo $success_message; ?>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Informasi Pendaftar</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Nama:</strong> <?php echo htmlspecialchars($applicant_name); ?></p>
                            <p><strong>Nomor Pendaftaran Saat Ini:</strong> <?php echo htmlspecialchars($old_registration_number); ?></p>
                            <p><strong>Jenjang Pendidikan:</strong> <?php echo htmlspecialchars($education_level); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Format Nomor Pendaftaran</h5>
                        </div>
                        <div class="card-body">
                            <p>Format nomor pendaftaran yang benar adalah: <strong>P26.2.001</strong></p>
                            <ul>
                                <li><strong>P/A</strong> - Jenjang pendidikan (P untuk SMP, A untuk SMA)</li>
                                <li><strong>26</strong> - Tahun pelajaran 2026</li>
                                <li><strong>2</strong> - Gelombang pendaftaran</li>
                                <li><strong>001</strong> - Urutan pendaftaran</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . "?id=" . $applicant_id); ?>" method="post">
                <div class="mb-3">
                    <label for="registration_number" class="form-label">Nomor Pendaftaran Baru</label>
                    <input type="text" name="registration_number" class="form-control <?php echo (!empty($registration_number_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $registration_number; ?>" placeholder="Contoh: P26.2.001">
                    <span class="invalid-feedback"><?php echo $registration_number_err; ?></span>
                    <div class="form-text">Pastikan format nomor pendaftaran sesuai dengan ketentuan.</div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="applicant_detail.php?id=<?php echo $applicant_id; ?>" class="btn btn-secondary">Batal</a>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
            
            <?php if (!empty($history)): ?>
            <div class="mt-5">
                <h5 class="mb-3">Riwayat Perubahan Nomor Pendaftaran</h5>
                
                <?php foreach ($history as $log): ?>
                <div class="history-card">
                    <div class="d-flex justify-content-between">
                        <div>
                            <p class="mb-1"><strong>Dari:</strong> <?php echo htmlspecialchars($log['old_number']); ?></p>
                            <p class="mb-1"><strong>Menjadi:</strong> <?php echo htmlspecialchars($log['new_number']); ?></p>
                        </div>
                        <div class="text-end">
                            <p class="mb-1"><small>Diubah oleh: <?php echo htmlspecialchars($log['changed_by_name']); ?></small></p>
                            <p class="mb-0"><small>Tanggal: <?php echo date('d F Y H:i', strtotime($log['changed_at'])); ?></small></p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; <?php echo date("Y"); ?> Seminari Menengah St. Petrus Canisius</p>
        </div>
    </footer>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
